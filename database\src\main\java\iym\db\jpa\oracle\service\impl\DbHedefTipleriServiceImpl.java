package iym.db.jpa.oracle.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.oracle.iym.HedefTipleri;
import iym.common.service.db.DbHedefTipleriService;
import iym.db.jpa.oracle.dao.HedefTipleriRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for HedefTipleri entity
 */
@Service
public class DbHedefTipleriServiceImpl extends GenericDbServiceImpl<HedefTipleri, Long> implements DbHedefTipleriService {

    private final HedefTipleriRepo hedefTipleriRepo;

    @Autowired
    public DbHedefTipleriServiceImpl(HedefTipleriRepo repository) {
        super(repository);
        this.hedefTipleriRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedefTipleri> findByHedefKodu(Long hedefKodu) {
        return hedefTipleriRepo.findByHedefKodu(hedefKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findByHedefTipi(String hedefTipi) {
        return hedefTipleriRepo.findByHedefTipi(hedefTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findBySonlandirmami(String sonlandirmami) {
        return hedefTipleriRepo.findBySonlandirmami(sonlandirmami);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findByDurum(String durum) {
        return hedefTipleriRepo.findByDurum(durum);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findByHedefTanim(String hedefTanim) {
        return hedefTipleriRepo.findByHedefTanim(hedefTanim);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findByAktifmi(Long aktifmi) {
        return hedefTipleriRepo.findByAktifmi(aktifmi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findByHitapaGonderilecekmi(Long hitapaGonderilecekmi) {
        return hedefTipleriRepo.findByHitapaGonderilecekmi(hitapaGonderilecekmi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findByHedefTipiContainingIgnoreCase(String hedefTipi) {
        return hedefTipleriRepo.findByHedefTipiContainingIgnoreCase(hedefTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedefTipleri> findAllByOrderBySnoAsc() {
        return hedefTipleriRepo.findAllByOrderBySnoAsc();
    }
}
