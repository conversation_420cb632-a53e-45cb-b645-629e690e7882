package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.MahkemeBilgi;
import iym.makos.dto.MahkemeBilgiDTO;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class MahkemeBilgiMapper {


    public MahkemeBilgiDTO toDto(MahkemeBilgi entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeBilgiDTO.builder()
                .mahkemeKodu(entity.getMahkemeKodu())
                .ilIlceKodu(entity.getIlIlceKodu())
                .mahkemeTuruKodu(entity.getMahkemeTuruKodu())
                .mahkemeSayi(entity.getMahkemeSayi())
                .mahkemeAdi(entity.getMahkemeAdi())
                .eklemeTarihi(entity.getEklemeTarihi())
                .ekleyenKullaniciId(entity.getEkleyenKullaniciId())
                .silindi(entity.getSilindi())
                .build();

    }


    public MahkemeBilgi toEntity(MahkemeBilgiDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeBilgi.builder()
                .mahkemeKodu(dto.getMahkemeKodu())
                .ilIlceKodu(dto.getIlIlceKodu())
                .mahkemeTuruKodu(dto.getMahkemeTuruKodu())
                .mahkemeSayi(dto.getMahkemeSayi())
                .mahkemeAdi(dto.getMahkemeAdi())
                .eklemeTarihi(dto.getEklemeTarihi())
                .ekleyenKullaniciId(dto.getEkleyenKullaniciId())
                .silindi(dto.getSilindi())
                .build();
    }


}
