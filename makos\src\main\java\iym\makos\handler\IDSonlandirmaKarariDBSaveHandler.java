package iym.makos.handler;

import iym.common.model.api.HedefTip;
import iym.common.model.entity.oracle.iym.*;
import iym.db.jpa.oracle.dao.*;
import iym.makos.dto.id.IDSonlandirmaKarariRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.HedefDetayID;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDSonlandirmaKarariDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSonlandirmaKarariRequest> {
    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;

    @Autowired
    public IDSonlandirmaKarariDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , KararRequestMapper kararRequestMapper
            , HedeflerDetayTalepRepo hedeflerDetayTalepRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;

    }

    @Override
    @Transactional
    public Long handleDbSave(IDSonlandirmaKarariRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            //List<MahkemeAidiyatTalep> savedMahkemeAidiyatList = saveMahkemeKararTalepAidiyatListesi(kararRequest.getMahkemeAidiyatKodlari(), mahkemeKararTalepId);

            //List<MahkemeSuclarTalep> savedMahkemeSucList = saveMahkemeKararTalepSucKoduListesi(kararRequest.getMahkemeSucTipiKodlari(), mahkemeKararTalepId);

            for (HedefDetayID hedefDetayID : request.getHedefDetayListesi()) {
                String hedefNo = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefNo();
                HedefTip hedefTipi = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefTip();

                //Uzatmaya konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = hedefDetayID.getIlgiliMahkemeKararDetayi();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
                Optional<Hedefler> iliskiliHedefOpt = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                if (iliskiliHedefOpt.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                }

                DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                //Eski sistemde
                HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper.toHedeflerDetayTalep(hedefDetayID, mahkemeKararTalepId, iliskiliHedefOpt.get().getId(), savedDMahkemeKararTalep.getId(), kullaniciId, kayitTarihi);
                HedeflerDetayTalep savedHedeflerDetayTalep = hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(hedefDetayID, mahkemeKararTalepId, kullaniciId, kayitTarihi);
                hedeflerTalepEntity.setKapatmaKararId(mahkemeKararOpt.get().getId());

                HedeflerTalep savedHedeflerTalep = hedeflerTalepRepo.save(hedeflerTalepEntity);
                //Hedef Aidiyat Talepleri Kaydet
                if (hedefDetayID.getHedefAidiyatKodlari() != null) {
                    List<HedeflerAidiyatTalep> savedHedefAidiyatList = saveHedeflerTalepSucKoduListesi(hedefDetayID.getHedefAidiyatKodlari(), savedHedeflerTalep.getId(),
                            savedHedeflerTalep.getHedefNo(), kayitTarihi, kullaniciId);
                }
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDSonlandirmaKarari handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

    private List<HedeflerAidiyatTalep> saveHedeflerTalepSucKoduListesi(List<String> sucKoduListesi, Long hedeflerTalepId, String hedefNo, Date islemTarihi, Long kullaniciId) throws Exception {
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : sucKoduListesi) {
            HedeflerAidiyatTalep hedeflerAidiyatTalep = kararRequestMapper.toHedeflerAidiyatTalep(kullaniciId, hedeflerTalepId, aidiyatKodu, islemTarihi);
            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = hedeflerAidiyatTalepRepo.save(hedeflerAidiyatTalep);
            result.add(savedHedeflerAidiyatTalep);
        }
        return result;
    }

    /*
    private List<MahkemeAidiyatTalep> saveMahkemeKararTalepAidiyatListesi(List<String> aidiyatListesi, Long mahkemeKararTalepId) throws Exception {
        List<MahkemeAidiyatTalep> result = new ArrayList<>();
        if (aidiyatListesi == null) {
            return result;
        }
        for (String aidiyatKodu : aidiyatListesi) {//kararRequest.getMahkemeKararDetayi().getAidiyatKodlari()) {
            MahkemeAidiyatTalep mahkemeAidiyatTalep = kararRequestMapper.toMahkemeAidiyatTalep(mahkemeKararTalepId, aidiyatKodu, null);
            MahkemeAidiyatTalep savedMahkemeAidiyatTalep = mahkemeAidiyatTalepRepo.save(mahkemeAidiyatTalep);
            if (savedMahkemeAidiyatTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_AIDIYAT_KAYDETMEHATASI, aidiyatKodu);
            } else {
                result.add(savedMahkemeAidiyatTalep);
            }
        }

        return result;
    }

    private List<MahkemeSuclarTalep> saveMahkemeKararTalepSucKoduListesi(List<String> sucKoduListesi, Long mahkemeKararTalepId) throws Exception {
        List<MahkemeSuclarTalep> result = new ArrayList<>();

        if (sucKoduListesi == null) {
            return result;
        }

        for (String sucTipiKodu : sucKoduListesi) {
            MahkemeSuclarTalep mahkemeSuclarTalep = kararRequestMapper.toMahkemeSuclarTalep(mahkemeKararTalepId, sucTipiKodu, null);
            MahkemeSuclarTalep savedMahkemeSuclarTalep = mahkemeSuclarTalepRepo.save(mahkemeSuclarTalep);
            if (savedMahkemeSuclarTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_SUCTIPI_KAYDETMEHATASI, sucTipiKodu);
            } else {
                result.add(savedMahkemeSuclarTalep);
            }
        }
        return result;
    }
     */

}

