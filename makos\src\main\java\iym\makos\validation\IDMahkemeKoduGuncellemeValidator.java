package iym.makos.validation;

import iym.common.model.api.KararTuru;
import iym.common.model.entity.oracle.iym.MahkemeBilgi;
import iym.common.model.entity.oracle.iym.MahkemeKarar;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDMahkemeKoduGuncellemeRequest;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKoduGuncellemeDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDMahkemeKoduGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDMahkemeKoduGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public IDMahkemeKoduGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService,
                                            DbMahkemeBilgiService dbMahkemeBilgiService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    public ValidationResult validate(IDMahkemeKoduGuncellemeRequest request) {

        try {

            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            for (MahkemeKoduGuncellemeDetay mahkemeKoduGuncellemeDetay : request.getMahkemeKoduGuncellemeDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = mahkemeKoduGuncellemeDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkil mahkeme karar boş olamaz");
                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format("Güncellemeye Konu Mahkeme Karar Bulunamadı: Mahheme İl/İlçe Kodu: %s Mahkeme Kodu: %s, Karar No: %s, Soruşturma No :%s"
                                , iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {
                        String yeniMahkemeKodu = mahkemeKoduGuncellemeDetay.getYeniMahkemeKodu();

                        if (yeniMahkemeKodu.equals(iliskiliMahkemeKararOpt.get().getMahkemeKodu())) {
                            validationResult.addFailedReason(yeniMahkemeKodu + " numaralı mahkeme kodu zaten eskisi ile aynıdır");
                        } else {
                            //Değiştirilmeye çalılşılan mahkeme kodu ile ilgili mahkeme var mı?
                            Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(yeniMahkemeKodu);
                            if (mahkemeBilgiOpt.isEmpty()) {
                                validationResult.addFailedReason(yeniMahkemeKodu + " numaralı mahkeme bilgisi bulunamadı.");
                            }
                        }

                    }
                }
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME;
    }

}

