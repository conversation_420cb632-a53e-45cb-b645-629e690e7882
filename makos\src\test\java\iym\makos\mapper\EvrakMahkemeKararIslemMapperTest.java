package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.EvrakMahkemeKararIslem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.EvrakMahkemeKararIslemDTO;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class EvrakMahkemeKararIslemMapperTest {

    private EvrakMahkemeKararIslemMapper evrakMahkemeKararIslemMapper;
    private EvrakMahkemeKararIslem evrakMahkemeKararIslem;
    private EvrakMahkemeKararIslemDTO evrakMahkemeKararIslemDTO;

    @BeforeEach
    void setUp() {
        evrakMahkemeKararIslemMapper = new EvrakMahkemeKararIslemMapper();

        evrakMahkemeKararIslem = EvrakMahkemeKararIslem.builder()
                .evrakId(1L)
                .kurum("01")
                .seviye("0")
                .build();

        evrakMahkemeKararIslemDTO = EvrakMahkemeKararIslemDTO.builder()
                .evrakId(1L)
                .kurum("01")
                .seviye("0")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        EvrakMahkemeKararIslemDTO result = evrakMahkemeKararIslemMapper.toDto(evrakMahkemeKararIslem);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEvrakId()).isEqualTo(evrakMahkemeKararIslem.getEvrakId());
        assertThat(result.getKurum()).isEqualTo(evrakMahkemeKararIslem.getKurum());
        assertThat(result.getSeviye()).isEqualTo(evrakMahkemeKararIslem.getSeviye());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        EvrakMahkemeKararIslemDTO result = evrakMahkemeKararIslemMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        EvrakMahkemeKararIslem result = evrakMahkemeKararIslemMapper.toEntity(evrakMahkemeKararIslemDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEvrakId()).isEqualTo(evrakMahkemeKararIslemDTO.getEvrakId());
        assertThat(result.getKurum()).isEqualTo(evrakMahkemeKararIslemDTO.getKurum());
        assertThat(result.getSeviye()).isEqualTo(evrakMahkemeKararIslemDTO.getSeviye());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        EvrakMahkemeKararIslem result = evrakMahkemeKararIslemMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        EvrakMahkemeKararIslem existingEntity = EvrakMahkemeKararIslem.builder()
                .evrakId(1L)
                .kurum("01")
                .seviye("0")
                .build();

        EvrakMahkemeKararIslemDTO updatedDto = EvrakMahkemeKararIslemDTO.builder()
                .evrakId(1L)
                .kurum("02")
                .seviye("1")
                .build();

        // When
        EvrakMahkemeKararIslem result = evrakMahkemeKararIslemMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEvrakId()).isEqualTo(1L); // ID should not change
        assertThat(result.getKurum()).isEqualTo("02");
        assertThat(result.getSeviye()).isEqualTo("1");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        EvrakMahkemeKararIslem existingEntity = EvrakMahkemeKararIslem.builder()
                .evrakId(1L)
                .kurum("01")
                .seviye("0")
                .build();

        // When
        EvrakMahkemeKararIslem result = evrakMahkemeKararIslemMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        EvrakMahkemeKararIslem result = evrakMahkemeKararIslemMapper.updateEntityFromDto(null, evrakMahkemeKararIslemDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<EvrakMahkemeKararIslem> entityList = Arrays.asList(evrakMahkemeKararIslem, evrakMahkemeKararIslem);

        // When
        List<EvrakMahkemeKararIslemDTO> result = evrakMahkemeKararIslemMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getEvrakId()).isEqualTo(evrakMahkemeKararIslem.getEvrakId());
        assertThat(result.get(1).getEvrakId()).isEqualTo(evrakMahkemeKararIslem.getEvrakId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<EvrakMahkemeKararIslemDTO> result = evrakMahkemeKararIslemMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<EvrakMahkemeKararIslemDTO> dtoList = Arrays.asList(evrakMahkemeKararIslemDTO, evrakMahkemeKararIslemDTO);

        // When
        List<EvrakMahkemeKararIslem> result = evrakMahkemeKararIslemMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getEvrakId()).isEqualTo(evrakMahkemeKararIslemDTO.getEvrakId());
        assertThat(result.get(1).getEvrakId()).isEqualTo(evrakMahkemeKararIslemDTO.getEvrakId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<EvrakMahkemeKararIslem> result = evrakMahkemeKararIslemMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
