package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.HedeflerDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface HedeflerDetayTalepRepo extends JpaRepository<HedeflerDetayTalep, Long> {

    List<HedeflerDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
