package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.DetayMahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for MahkemeKararTalep entity
 */
@Repository
public interface DetayMahkemeKararTalepRepo extends JpaRepository<DetayMahkemeKararTalep, Long> {

    List<DetayMahkemeKararTalep> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
