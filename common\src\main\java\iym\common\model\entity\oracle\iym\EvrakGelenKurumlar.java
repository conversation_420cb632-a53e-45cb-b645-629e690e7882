package iym.common.model.entity.oracle.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for EVRAK_GELEN_KURUMLAR table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "EvrakGelenKurumlar")
@Table(name = "EVRAK_GELEN_KURUMLAR")
public class EvrakGelenKurumlar implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "KURUM_KOD", nullable = false, length = 10)
    @NotNull
    @Size(max = 10)
    private String kurumKod;

    @Column(name = "KURUM_ADI", nullable = false, length = 50)
    @NotNull
    @Size(max = 50)
    private String kurumAdi;

    @Column(name = "KURUM", length = 64)
    @Size(max = 64)
    private String kurum;

    @Column(name = "IDX")
    private Long idx;
}
