package iym.common.service.db;


import iym.common.model.entity.oracle.makos.MakosUser;
import iym.common.model.enums.MakosUserRoleType;
import iym.common.model.enums.UserStatusType;

import java.util.List;
import java.util.Optional;

public interface DbMakosUserService extends GenericDbService<MakosUser, Long> {
    Optional<MakosUser> findByUsername(String name);

    List<MakosUser> findByStatus(UserStatusType status);

    List<MakosUser> findByRoleOrderByUsernameAsc(MakosUserRoleType role);

    List<MakosUser> findAllByOrderByUsernameAsc();

    void activateUser(String username);

    void deactivateUser(String username);

    void updateUser(MakosUser makosUser);

}
