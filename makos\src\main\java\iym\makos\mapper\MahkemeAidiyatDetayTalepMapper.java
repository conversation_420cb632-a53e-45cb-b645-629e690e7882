package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.MahkemeAidiyatDetayTalep;
import org.springframework.stereotype.Component;
import iym.makos.dto.MahkemeAidiyatDetayTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeAidiyatDetayTalep entity and DTO
 */
@Component
public class MahkemeAidiyatDetayTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity MahkemeAidiyatDetayTalep entity
     * @return MahkemeAidiyatDetayTalepDTO
     */
    public MahkemeAidiyatDetayTalepDTO toDto(MahkemeAidiyatDetayTalep entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeAidiyatDetayTalepDTO.builder()
                .id(entity.getId())
                .iliskiliMahkemeKararId(entity.getIliskiliMahkemeKararId())
                .mahkemeKararId(entity.getMahkemeKararTalepId())
                .mahkemeAidiyatKoduEkle(entity.getMahkemeAidiyatKoduEkle())
                .mahkemeAidiyatKoduCikar(entity.getMahkemeAidiyatKoduCikar())
                .tarih(entity.getTarih())
                .durum(entity.getDurum())
                .mahkemeKararDetayId(entity.getMahkemeKararDetayTalepId())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto MahkemeAidiyatDetayTalepDTO
     * @return MahkemeAidiyatDetayTalep entity
     */
    public MahkemeAidiyatDetayTalep toEntity(MahkemeAidiyatDetayTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeAidiyatDetayTalep.builder()
                .id(dto.getId())
                .iliskiliMahkemeKararId(dto.getIliskiliMahkemeKararId())
                .mahkemeKararTalepId(dto.getMahkemeKararId())
                .mahkemeAidiyatKoduEkle(dto.getMahkemeAidiyatKoduEkle())
                .mahkemeAidiyatKoduCikar(dto.getMahkemeAidiyatKoduCikar())
                .tarih(dto.getTarih())
                .durum(dto.getDurum())
                .mahkemeKararDetayTalepId(dto.getMahkemeKararDetayId())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity MahkemeAidiyatDetayTalep entity to update
     * @param dto MahkemeAidiyatDetayTalepDTO with new values
     * @return Updated MahkemeAidiyatDetayTalep entity
     */
    public MahkemeAidiyatDetayTalep updateEntityFromDto(MahkemeAidiyatDetayTalep entity, MahkemeAidiyatDetayTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setIliskiliMahkemeKararId(dto.getIliskiliMahkemeKararId());
        entity.setMahkemeKararTalepId(dto.getMahkemeKararId());
        entity.setMahkemeAidiyatKoduEkle(dto.getMahkemeAidiyatKoduEkle());
        entity.setMahkemeAidiyatKoduCikar(dto.getMahkemeAidiyatKoduCikar());
        entity.setTarih(dto.getTarih());
        entity.setDurum(dto.getDurum());
        entity.setMahkemeKararDetayTalepId(dto.getMahkemeKararDetayId());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities List of MahkemeAidiyatDetayTalep entities
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> toDtoList(List<MahkemeAidiyatDetayTalep> entities) {
        if (entities == null) {
            return List.of();
        }

        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
