package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.CanakNumaralar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for CanakNumaralar entity
 */
@Repository
public interface CanakNumaralarRepo extends JpaRepository<CanakNumaralar, Long> {

    Optional<CanakNumaralar> findByCanakNo(String canakNo);
    
    List<CanakNumaralar> findByCanakNoContainingIgnoreCase(String canakNo);
    
    List<CanakNumaralar> findByKurumKod(String kurumKod);
    
    List<CanakNumaralar> findByEkleyenId(Long ekleyenId);
    
    List<CanakNumaralar> findByKutu(Long kutu);
    
    List<CanakNumaralar> findByEklemeTarihBetween(Date startDate, Date endDate);
    
    List<CanakNumaralar> findByAciklamaContainingIgnoreCase(String aciklama);
    
    List<CanakNumaralar> findByKurumKodAndKutu(String kurumKod, Long kutu);
    
    List<CanakNumaralar> findByCanakNoStartingWith(String prefix);
    
    boolean existsByCanakNo(String canakNo);
}
