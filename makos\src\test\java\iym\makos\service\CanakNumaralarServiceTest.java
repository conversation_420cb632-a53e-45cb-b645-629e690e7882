package iym.makos.service;

import iym.common.model.entity.oracle.iym.CanakNumaralar;
import iym.common.service.db.DbCanakNumaralarService;
import iym.makos.dto.CanakNumaralarDTO;
import iym.makos.mapper.CanakNumaralarMapper;
import iym.makos.service.makos.CanakNumaralarService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CanakNumaralarServiceTest {

    @Mock
    private DbCanakNumaralarService dbCanakNumaralarService;

    @Mock
    private CanakNumaralarMapper canakNumaralarMapper;

    @InjectMocks
    private CanakNumaralarService canakNumaralarService;

    private CanakNumaralar canakNumaralar;
    private CanakNumaralarDTO canakNumaralarDTO;
    private List<CanakNumaralar> canakNumaralarList;
    private List<CanakNumaralarDTO> canakNumaralarDTOList;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();

        canakNumaralar = CanakNumaralar.builder()
                .id(1L)
                .canakNo("CANAK001")
                .kurumKod("01")
                .eklemeTarih(testDate)
                .kutu(1L)
                .aciklama("Test açıklama")
                .ekleyenId(100L)
                .build();

        CanakNumaralar canakNumaralar2 = CanakNumaralar.builder()
                .id(2L)
                .canakNo("CANAK002")
                .kurumKod("02")
                .eklemeTarih(testDate)
                .kutu(2L)
                .aciklama("Test açıklama 2")
                .ekleyenId(101L)
                .build();

        canakNumaralarDTO = CanakNumaralarDTO.builder()
                .id(1L)
                .canakNo("CANAK001")
                .kurumKod("01")
                .eklemeTarih(testDate)
                .kutu(1L)
                .aciklama("Test açıklama")
                .ekleyenId(100L)
                .build();

        CanakNumaralarDTO canakNumaralarDTO2 = CanakNumaralarDTO.builder()
                .id(2L)
                .canakNo("CANAK002")
                .kurumKod("02")
                .eklemeTarih(testDate)
                .kutu(2L)
                .aciklama("Test açıklama 2")
                .ekleyenId(101L)
                .build();

        canakNumaralarList = Arrays.asList(canakNumaralar, canakNumaralar2);
        canakNumaralarDTOList = Arrays.asList(canakNumaralarDTO, canakNumaralarDTO2);
    }

    @Test
    void findAll_shouldReturnAllCanakNumaralar() {
        // Given
        when(dbCanakNumaralarService.findAll()).thenReturn(canakNumaralarList);
        when(canakNumaralarMapper.toDtoList(canakNumaralarList)).thenReturn(canakNumaralarDTOList);

        // When
        List<CanakNumaralarDTO> result = canakNumaralarService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(canakNumaralarDTOList);
        verify(dbCanakNumaralarService).findAll();
        verify(canakNumaralarMapper).toDtoList(canakNumaralarList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfCanakNumaralar() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<CanakNumaralar> canakNumaralarPage = new PageImpl<>(canakNumaralarList, pageable, canakNumaralarList.size());
        
        when(dbCanakNumaralarService.findAll(pageable)).thenReturn(canakNumaralarPage);
        when(canakNumaralarMapper.toDtoList(canakNumaralarList)).thenReturn(canakNumaralarDTOList);

        // When
        Page<CanakNumaralarDTO> result = canakNumaralarService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(canakNumaralarDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbCanakNumaralarService).findAll(pageable);
        verify(canakNumaralarMapper).toDtoList(canakNumaralarList);
    }

    @Test
    void findById_shouldReturnCanakNumaralar_whenExists() {
        // Given
        when(dbCanakNumaralarService.findById(1L)).thenReturn(Optional.of(canakNumaralar));
        when(canakNumaralarMapper.toDto(canakNumaralar)).thenReturn(canakNumaralarDTO);

        // When
        CanakNumaralarDTO result = canakNumaralarService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(canakNumaralarDTO);
        verify(dbCanakNumaralarService).findById(1L);
        verify(canakNumaralarMapper).toDto(canakNumaralar);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbCanakNumaralarService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> canakNumaralarService.findById(1L));
        verify(dbCanakNumaralarService).findById(1L);
        verify(canakNumaralarMapper, never()).toDto(any());
    }

    @Test
    void findByCanakNo_shouldReturnCanakNumaralar_whenExists() {
        // Given
        when(dbCanakNumaralarService.findByCanakNo("CANAK001")).thenReturn(Optional.of(canakNumaralar));
        when(canakNumaralarMapper.toDto(canakNumaralar)).thenReturn(canakNumaralarDTO);

        // When
        CanakNumaralarDTO result = canakNumaralarService.findByCanakNo("CANAK001");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(canakNumaralarDTO);
        verify(dbCanakNumaralarService).findByCanakNo("CANAK001");
        verify(canakNumaralarMapper).toDto(canakNumaralar);
    }

    @Test
    void findByCanakNo_shouldThrowException_whenNotExists() {
        // Given
        when(dbCanakNumaralarService.findByCanakNo("CANAK001")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> canakNumaralarService.findByCanakNo("CANAK001"));
        verify(dbCanakNumaralarService).findByCanakNo("CANAK001");
        verify(canakNumaralarMapper, never()).toDto(any());
    }

    @Test
    void findByCanakNoContainingIgnoreCase_shouldReturnCanakNumaralarList() {
        // Given
        when(dbCanakNumaralarService.findByCanakNoContainingIgnoreCase("CANAK")).thenReturn(Arrays.asList(canakNumaralar));
        when(canakNumaralarMapper.toDtoList(Arrays.asList(canakNumaralar))).thenReturn(Arrays.asList(canakNumaralarDTO));

        // When
        List<CanakNumaralarDTO> result = canakNumaralarService.findByCanakNoContainingIgnoreCase("CANAK");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(canakNumaralarDTO);
        verify(dbCanakNumaralarService).findByCanakNoContainingIgnoreCase("CANAK");
        verify(canakNumaralarMapper).toDtoList(Arrays.asList(canakNumaralar));
    }

    @Test
    void create_shouldCreateCanakNumaralar() {
        // Given
        when(dbCanakNumaralarService.existsByCanakNo("CANAK001")).thenReturn(false);
        when(canakNumaralarMapper.toEntity(canakNumaralarDTO)).thenReturn(canakNumaralar);
        when(canakNumaralarMapper.toDto(canakNumaralar)).thenReturn(canakNumaralarDTO);

        // When
        CanakNumaralarDTO result = canakNumaralarService.create(canakNumaralarDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(canakNumaralarDTO);
        verify(dbCanakNumaralarService).existsByCanakNo("CANAK001");
        verify(canakNumaralarMapper).toEntity(canakNumaralarDTO);
        verify(dbCanakNumaralarService).save(canakNumaralar);
        verify(canakNumaralarMapper).toDto(canakNumaralar);
    }

    @Test
    void create_shouldThrowException_whenCanakNumaralarAlreadyExists() {
        // Given
        when(dbCanakNumaralarService.existsByCanakNo("CANAK001")).thenReturn(true);

        // When/Then
        assertThrows(ResponseStatusException.class, () -> canakNumaralarService.create(canakNumaralarDTO));
        verify(dbCanakNumaralarService).existsByCanakNo("CANAK001");
        verify(canakNumaralarMapper, never()).toEntity(any());
        verify(dbCanakNumaralarService, never()).save(any());
        verify(canakNumaralarMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateCanakNumaralar() {
        // Given
        when(dbCanakNumaralarService.findById(1L)).thenReturn(Optional.of(canakNumaralar));
        when(canakNumaralarMapper.updateEntityFromDto(canakNumaralar, canakNumaralarDTO)).thenReturn(canakNumaralar);
        when(canakNumaralarMapper.toDto(canakNumaralar)).thenReturn(canakNumaralarDTO);

        // When
        CanakNumaralarDTO result = canakNumaralarService.update(1L, canakNumaralarDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(canakNumaralarDTO);
        verify(dbCanakNumaralarService).findById(1L);
        verify(canakNumaralarMapper).updateEntityFromDto(canakNumaralar, canakNumaralarDTO);
        verify(dbCanakNumaralarService).update(canakNumaralar);
        verify(canakNumaralarMapper).toDto(canakNumaralar);
    }

    @Test
    void update_shouldThrowException_whenCanakNumaralarNotExists() {
        // Given
        when(dbCanakNumaralarService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> canakNumaralarService.update(1L, canakNumaralarDTO));
        verify(dbCanakNumaralarService).findById(1L);
        verify(canakNumaralarMapper, never()).updateEntityFromDto(any(), any());
        verify(dbCanakNumaralarService, never()).update(any());
        verify(canakNumaralarMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteCanakNumaralar() {
        // Given
        when(dbCanakNumaralarService.findById(1L)).thenReturn(Optional.of(canakNumaralar));

        // When
        canakNumaralarService.delete(1L);

        // Then
        verify(dbCanakNumaralarService).findById(1L);
        verify(dbCanakNumaralarService).delete(canakNumaralar);
    }

    @Test
    void delete_shouldThrowException_whenCanakNumaralarNotExists() {
        // Given
        when(dbCanakNumaralarService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> canakNumaralarService.delete(1L));
        verify(dbCanakNumaralarService).findById(1L);
        verify(dbCanakNumaralarService, never()).delete(any());
    }
}
