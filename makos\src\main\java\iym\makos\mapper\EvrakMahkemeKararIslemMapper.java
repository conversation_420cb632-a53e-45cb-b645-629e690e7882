package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.EvrakMahkemeKararIslem;
import org.springframework.stereotype.Component;
import iym.makos.dto.EvrakMahkemeKararIslemDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for EvrakMahkemeKararIslem entity and DTO
 */
@Component
public class EvrakMahkemeKararIslemMapper {

    /**
     * Convert entity to DTO
     * @param entity EvrakMahkemeKararIslem entity
     * @return EvrakMahkemeKararIslemDTO
     */
    public EvrakMahkemeKararIslemDTO toDto(EvrakMahkemeKararIslem entity) {
        if (entity == null) {
            return null;
        }

        return EvrakMahkemeKararIslemDTO.builder()
                .evrakId(entity.getEvrakId())
                .kurum(entity.getKurum())
                .seviye(entity.getSeviye())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto EvrakMahkemeKararIslemDTO
     * @return EvrakMahkemeKararIslem entity
     */
    public EvrakMahkemeKararIslem toEntity(EvrakMahkemeKararIslemDTO dto) {
        if (dto == null) {
            return null;
        }

        return EvrakMahkemeKararIslem.builder()
                .evrakId(dto.getEvrakId())
                .kurum(dto.getKurum())
                .seviye(dto.getSeviye())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity EvrakMahkemeKararIslem entity to update
     * @param dto EvrakMahkemeKararIslemDTO with new values
     * @return Updated EvrakMahkemeKararIslem entity
     */
    public EvrakMahkemeKararIslem updateEntityFromDto(EvrakMahkemeKararIslem entity, EvrakMahkemeKararIslemDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        // evrakId is the primary key and should not be updated
        entity.setKurum(dto.getKurum());
        entity.setSeviye(dto.getSeviye());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of EvrakMahkemeKararIslem entities
     * @return List of EvrakMahkemeKararIslemDTO
     */
    public List<EvrakMahkemeKararIslemDTO> toDtoList(List<EvrakMahkemeKararIslem> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of EvrakMahkemeKararIslemDTO
     * @return List of EvrakMahkemeKararIslem entities
     */
    public List<EvrakMahkemeKararIslem> toEntityList(List<EvrakMahkemeKararIslemDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
