package iym.makos.validation;

import iym.common.model.entity.oracle.iym.EvrakGelenKurumlar;
import iym.common.model.entity.oracle.iym.Iller;
import iym.common.model.entity.oracle.iym.MahkemeBilgi;
import iym.common.service.db.DbEvrakGelenKurumlarService;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.validation.ValidationResult;
import iym.makos.model.reqrep.MahkemeKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class MahkemeKararRequestCommonValidator {

    private final DbIllerService dbIllerService;
    private final DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public MahkemeKararRequestCommonValidator(DbIllerService dbIllerService,
                                              DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService,
                                              DbMahkemeBilgiService dbMahkemeBilgiService) {
        this.dbIllerService = dbIllerService;
        this.dbEvrakGelenKurumlarService = dbEvrakGelenKurumlarService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    public ValidationResult validate(MahkemeKararRequest request) {

        try {
            ValidationResult validationResult = request.isValid();

            if (!validationResult.isValid()) {
                return validationResult;
            }

            //EVRAK Kontrolleri
            String evrakGelenIlIlceKodu = request.getEvrakDetay().getGeldigiIlIlceKodu();
            Optional<Iller> ilIlceOpt = dbIllerService.getByIlIlceKodu(evrakGelenIlIlceKodu);
            if (ilIlceOpt.isEmpty()) {
                validationResult.addFailedReason(String.format("%s koduna sahip  evrak il/ilçe verisi sistemde bulunamadı", evrakGelenIlIlceKodu));
            }

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            Optional<EvrakGelenKurumlar> evrakGelenKurumlarOpt = dbEvrakGelenKurumlarService.findByKurumKod(evrakGelenKurumKodu);
            if (evrakGelenKurumlarOpt.isEmpty()) {
                validationResult.addFailedReason(String.format("%s numaralı evrak kurum kodu sistemde bulunamadı", evrakGelenIlIlceKodu));
            }

            //Mahkeme Karar Kontrolleri
            String mahkemeKodu = request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            //Butun tiplerde mahkeme karar bulunmak zorunda oldugu icin mahkeme kodundan mahkeme bilgilerini kontrol et.
            Optional<MahkemeBilgi> mahkemeBilgiDb = dbMahkemeBilgiService.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgiDb.isEmpty()) {
                validationResult.addFailedReason(mahkemeKodu + " kodu ile kayıtlı bir mahkeme bilgisi bulunamamıştır.");
            }

            Optional<Iller> mahkemeilIlceDb = dbIllerService.getByIlIlceKodu(request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeIlIlceKodu());
            if (mahkemeilIlceDb.isEmpty()) {
                validationResult.addFailedReason(String.format("%s koduna sahip  mahkeme il/ilçe verisi sistemde bulunamadı", evrakGelenIlIlceKodu));
            }

            return validationResult;

        } catch (Exception ex) {
            log.error("Validation failed", ex);
            return new ValidationResult("Validation failed. Internal error");
        }
    }
}
