# Entity ve Repository Reorganizasyon Planı

## 🎯 Amaç
Mevcut tek datasource yapısını multi-datasource yapısına uygun hale getirmek için entity'leri ve repository'leri yeniden organize etmek.

## 📁 Yeni Package Yapısı

### Entity Package Yapısı
```
common/src/main/java/iym/common/model/entity/
├── oracle/
│   ├── iym/                    # IYM Oracle entities
│   │   ├── CanakNumaralar.java
│   │   ├── DetayMahkemeKararTalep.java
│   │   ├── EvrakFiles.java
│   │   ├── EvrakGelenKurumlar.java
│   │   ├── EvrakKayit.java
│   │   ├── EvrakMahkemeKararIslem.java
│   │   ├── EvrakSiraNo.java
│   │   ├── Gorevler2.java
│   │   ├── Gorevler2PK.java
│   │   ├── HedefTipleri.java
│   │   ├── Hedefler.java
│   │   ├── HedeflerAidiyatTalep.java
│   │   ├── HedeflerDetayTalep.java
│   │   ├── HedeflerTalep.java
│   │   ├── HtsHedeflerTalep.java
│   │   ├── HtsMahkemeKararTalep.java
│   │   ├── Iller.java
│   │   ├── IymUser.java
│   │   ├── Kullanicilar.java
│   │   ├── MahkemeAidiyat.java
│   │   ├── MahkemeAidiyatDetayTalep.java
│   │   ├── MahkemeAidiyatTalep.java
│   │   ├── MahkemeBilgi.java
│   │   ├── MahkemeHedeflerAidiyatTalep.java
│   │   ├── MahkemeKarar.java
│   │   ├── MahkemeKararTalep.java
│   │   ├── MahkemeKoduDetayTalep.java
│   │   ├── MahkemeSuclarTalep.java
│   │   └── SucTipi.java
│   └── makos/                  # MAKOS Oracle entities
│       ├── MakosUser.java
│       ├── MakosUserAuditLog.java
│       └── log/
│           └── MakosKararRequestLog.java
└── postgresql/                 # Future PostgreSQL entities
    └── iym/
        └── (future entities)
```

### Repository Package Yapısı
```
database/src/main/java/iym/db/jpa/
├── oracle/
│   └── dao/                    # Oracle repositories
│       ├── CanakNumaralarRepo.java
│       ├── DetayMahkemeKararTalepRepo.java
│       ├── EvrakFilesRepo.java
│       ├── EvrakGelenKurumlarRepo.java
│       ├── EvrakKayitRepo.java
│       ├── EvrakMahkemeKararIslemRepo.java
│       ├── EvrakSiraNoRepo.java
│       ├── Gorevler2Repo.java
│       ├── HedefTipleriRepo.java
│       ├── HedeflerAidiyatTalepRepo.java
│       ├── HedeflerDetayTalepRepo.java
│       ├── HedeflerRepo.java
│       ├── HedeflerTalepRepo.java
│       ├── HtsHedeflerTalepRepo.java
│       ├── HtsMahkemeKararTalepRepo.java
│       ├── IllerRepo.java
│       ├── IymUserRepo.java
│       ├── KullanicilarRepo.java
│       ├── MahkemeAidiyatDetayTalepRepo.java
│       ├── MahkemeAidiyatRepo.java
│       ├── MahkemeAidiyatTalepRepo.java
│       ├── MahkemeBilgisiRepo.java
│       ├── MahkemeHedeflerAidiyatTalepRepo.java
│       ├── MahkemeKararRepo.java
│       ├── MahkemeKararTalepRepo.java
│       ├── MahkemeKoduDetayTalepRepo.java
│       ├── MahkemeSuclarTalepRepo.java
│       ├── MakosKararRequestLogRepo.java
│       ├── MakosUserAuditLogRepo.java
│       ├── MakosUserRepo.java
│       └── SucTipiRepo.java
└── postgresql/                 # Future PostgreSQL repositories
    └── dao/
        └── (future repositories)
```

## 🔄 Migration Adımları

### Adım 1: Yeni Package Yapısını Oluştur
```bash
mkdir -p common/src/main/java/iym/common/model/entity/oracle/iym
mkdir -p common/src/main/java/iym/common/model/entity/oracle/makos/log
mkdir -p common/src/main/java/iym/common/model/entity/postgresql/iym
mkdir -p database/src/main/java/iym/db/jpa/oracle/dao
mkdir -p database/src/main/java/iym/db/jpa/postgresql/dao
```

### Adım 2: Entity'leri Taşı
```bash
# IYM entities
mv common/src/main/java/iym/common/model/entity/iym/* \
   common/src/main/java/iym/common/model/entity/oracle/iym/

# MAKOS entities
mv common/src/main/java/iym/common/model/entity/makos/* \
   common/src/main/java/iym/common/model/entity/oracle/makos/
```

### Adım 3: Repository'leri Taşı
```bash
# Tüm repository'leri Oracle package'ına taşı
mv database/src/main/java/iym/db/jpa/dao/* \
   database/src/main/java/iym/db/jpa/oracle/dao/
```

### Adım 4: Import Statements'ları Güncelle
Tüm dosyalarda package import'larını güncelle:
- `iym.common.model.entity.iym.*` → `iym.common.model.entity.oracle.iym.*`
- `iym.common.model.entity.makos.*` → `iym.common.model.entity.oracle.makos.*`
- `iym.db.jpa.dao.*` → `iym.db.jpa.oracle.dao.*`

## 🔧 Gerekli Kod Değişiklikleri

### Service Layer Güncellemeleri
```java
// Örnek: DbMahkemeBilgiServiceImpl.java
import iym.common.model.entity.oracle.iym.MahkemeBilgi;
import iym.db.jpa.oracle.dao.MahkemeBilgisiRepo;
```

### Controller Layer Güncellemeleri
```java
// Örnek: MahkemeKararController.java
import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import iym.common.model.entity.oracle.makos.MakosUser;
```

## ⚠️ Dikkat Edilmesi Gerekenler

1. **Gradual Migration**: Tüm değişiklikleri aynı anda yapmak yerine, modül modül geçiş yapın
2. **Test Coverage**: Her değişiklikten sonra testleri çalıştırın
3. **Backup**: Değişiklik öncesi mevcut kodu yedekleyin
4. **Import Cleanup**: IDE'nin auto-import özelliğini kullanarak import'ları temizleyin

## 🎯 Sonraki Adımlar

1. **Mevcut DbLoader'ı Conditional Yap**
2. **OracleJpaConfig'i Aktif Et**
3. **Application Properties Güncelle**
4. **Integration Testleri Çalıştır**
