# Multi-DataSource Application Properties Konfigürasyonu

## 🎯 Amaç
Mevcut tek Oracle datasource yapısını multi-datasource yapısına dönüştürmek için application.properties dosyasını güncellemek.

## 📝 Yeni Application Properties Yapısı

### Backend Modülü - application.properties
```properties
# =============================================================================
# MULTI-DATASOURCE CONFIGURATION
# =============================================================================

# DataSource Mode Configuration
app.datasource.mode=multi

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
server.port=4000
server.servlet.context-path=/
server.tomcat.additional-tld-skip-patterns=*mchange-commons-java*.jar

# Default profile (development)
spring.profiles.active=dev

# =============================================================================
# ORACLE DATABASE CONFIGURATION (PRIMARY)
# =============================================================================
spring.datasource.oracle.url=***********************************
spring.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=iym
spring.datasource.oracle.password=iym

# Oracle HikariCP Configuration
spring.datasource.oracle.hikari.connection-timeout=20000
spring.datasource.oracle.hikari.maximum-pool-size=10
spring.datasource.oracle.hikari.minimum-idle=5
spring.datasource.oracle.hikari.idle-timeout=300000
spring.datasource.oracle.hikari.max-lifetime=1200000
spring.datasource.oracle.hikari.leak-detection-threshold=60000
spring.datasource.oracle.hikari.pool-name=OracleHikariPool

# =============================================================================
# POSTGRESQL DATABASE CONFIGURATION (SECONDARY - FUTURE)
# =============================================================================
# spring.datasource.postgresql.url=***************************************
# spring.datasource.postgresql.driver-class-name=org.postgresql.Driver
# spring.datasource.postgresql.username=iym_user
# spring.datasource.postgresql.password=iym_password

# PostgreSQL HikariCP Configuration (FUTURE)
# spring.datasource.postgresql.hikari.connection-timeout=20000
# spring.datasource.postgresql.hikari.maximum-pool-size=10
# spring.datasource.postgresql.hikari.minimum-idle=5
# spring.datasource.postgresql.hikari.idle-timeout=300000
# spring.datasource.postgresql.hikari.max-lifetime=1200000
# spring.datasource.postgresql.hikari.leak-detection-threshold=60000
# spring.datasource.postgresql.hikari.pool-name=PostgreSQLHikariPool

# =============================================================================
# JPA CONFIGURATION (GLOBAL)
# =============================================================================
# Disable default auto-configuration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Multi-datasource specific logging
logging.level.iym.common.config=DEBUG
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.transaction=DEBUG

# =============================================================================
# SWAGGER CONFIGURATION
# =============================================================================
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# =============================================================================
# APPLICATION SPECIFIC PROPERTIES
# =============================================================================
app.init-db=true

# JWT Configuration
app.jwtSecret=${JWT_SECRET:mysecretkeyasdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb}
app.jwtExpirationInSec=${JWT_EXPIRATION_SEC:86400}

# CORS Configuration
cors.allowed.origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000}

# MAKOS API Configuration
makos.api.base-url=${MAKOS_API_BASE_URL:http://localhost:5000}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}
```

### Makos Modülü - application.properties
```properties
# =============================================================================
# MULTI-DATASOURCE CONFIGURATION
# =============================================================================

# DataSource Mode Configuration
app.datasource.mode=multi

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
server.port=5000
server.servlet.context-path=/makosapi

# Default profile (development)
spring.profiles.active=dev

# =============================================================================
# ORACLE DATABASE CONFIGURATION (PRIMARY)
# =============================================================================
spring.datasource.oracle.url=***********************************
spring.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=iym
spring.datasource.oracle.password=iym

# Oracle HikariCP Configuration
spring.datasource.oracle.hikari.connection-timeout=20000
spring.datasource.oracle.hikari.maximum-pool-size=15
spring.datasource.oracle.hikari.minimum-idle=5
spring.datasource.oracle.hikari.idle-timeout=300000
spring.datasource.oracle.hikari.max-lifetime=1200000
spring.datasource.oracle.hikari.leak-detection-threshold=60000
spring.datasource.oracle.hikari.pool-name=MakosOracleHikariPool

# =============================================================================
# JPA CONFIGURATION (GLOBAL)
# =============================================================================
# Disable default auto-configuration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Multi-datasource specific logging
logging.level.iym.common.config=DEBUG

# =============================================================================
# SWAGGER CONFIGURATION
# =============================================================================
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# =============================================================================
# APPLICATION SPECIFIC PROPERTIES
# =============================================================================
app.init-db=true
```

## 🔄 Migration Adımları

### Adım 1: Mevcut Properties'i Yedekle
```bash
cp backend/src/main/resources/application.properties backend/src/main/resources/application.properties.backup
cp makos/src/main/resources/application.properties makos/src/main/resources/application.properties.backup
```

### Adım 2: Yeni Properties'i Uygula
Yukarıdaki konfigürasyonları ilgili dosyalara kopyala.

### Adım 3: Test Konfigürasyonu
Test ortamı için ayrı properties dosyası oluştur:
```properties
# application-test.properties
app.datasource.mode=single
# Test için mevcut konfigürasyon
```

## ⚠️ Önemli Notlar

1. **Backward Compatibility**: `app.datasource.mode=single` ile eski yapı korunuyor
2. **PostgreSQL Hazırlığı**: PostgreSQL konfigürasyonu comment olarak hazır
3. **Connection Pool**: Her datasource için ayrı HikariCP ayarları
4. **Auto-Configuration**: Spring Boot'un default datasource auto-config'i devre dışı
5. **Logging**: Multi-datasource debug için ek logging seviyeleri

## 🎯 Sonraki Adımlar

1. Properties dosyalarını güncelle
2. OracleJpaConfig'i aktif et
3. Testleri çalıştır
4. PostgreSQL konfigürasyonu için hazırlık yap
