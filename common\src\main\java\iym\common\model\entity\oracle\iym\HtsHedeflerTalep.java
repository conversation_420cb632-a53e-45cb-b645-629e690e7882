package iym.common.model.entity.oracle.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for HTS_HEDEFLER_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HtsHedeflerTalep")
@Table(name = "HTS_HEDEFLER_TALEP")
public class HtsHedeflerTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HTS_HEDEFLER_TALEP_SEQ")
    @SequenceGenerator(name = "HTS_HEDEFLER_TALEP_SEQ", sequenceName = "HTS_HEDEFLER_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararId;

    @Column(name = "HEDEF_NO", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String hedefNo;

    @Column(name = "KARSI_HEDEF_NO", length = 100)
    @Size(max = 100)
    private String karsiHedefNo;

    @Column(name = "SORGU_TIPI", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String sorguTipi;

    @Column(name = "BASLANGIC_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date baslangicTarihi;

    @Column(name = "BITIS_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date bitisTarihi;

    @Column(name = "TESPIT_TURU", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String tespitTuru;

    @Column(name = "KULLANICI_ID", nullable = false, length = 10)
    @NotNull
    @Size(max = 10)
    private String kullaniciId;

    @Column(name = "DURUMU", length = 100)
    @Size(max = 100)
    private String durumu;
}
