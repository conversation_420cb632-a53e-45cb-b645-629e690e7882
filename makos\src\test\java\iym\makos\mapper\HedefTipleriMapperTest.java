package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.HedefTipleri;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.HedefTipleriDTO;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class HedefTipleriMapperTest {

    private HedefTipleriMapper hedefTipleriMapper;
    private HedefTipleri hedefTipleri;
    private HedefTipleriDTO hedefTipleriDTO;

    @BeforeEach
    void setUp() {
        hedefTipleriMapper = new HedefTipleriMapper();

        hedefTipleri = HedefTipleri.builder()
                .id(1L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON")
                .sonlandirmami("E")
                .karsiligi(1L)
                .sno(1L)
                .hedefTanim("TEL")
                .durum("AKTIF")
                .hitapTip("TEL")
                .hitapIcerikTip("NUM")
                .hitapIcindemi("E")
                .hitapEh("E")
                .minl(10L)
                .maxl(10L)
                .imhaYapilsinmi("E")
                .tasinabilirmi("E")
                .aktifmi(1L)
                .hitapaGonderilecekmi(0L)
                .build();

        hedefTipleriDTO = HedefTipleriDTO.builder()
                .id(1L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON")
                .sonlandirmami("E")
                .karsiligi(1L)
                .sno(1L)
                .hedefTanim("TEL")
                .durum("AKTIF")
                .hitapTip("TEL")
                .hitapIcerikTip("NUM")
                .hitapIcindemi("E")
                .hitapEh("E")
                .minl(10L)
                .maxl(10L)
                .imhaYapilsinmi("E")
                .tasinabilirmi("E")
                .aktifmi(1L)
                .hitapaGonderilecekmi(0L)
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        HedefTipleriDTO result = hedefTipleriMapper.toDto(hedefTipleri);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(hedefTipleri.getId());
        assertThat(result.getHedefKodu()).isEqualTo(hedefTipleri.getHedefKodu());
        assertThat(result.getHedefTipi()).isEqualTo(hedefTipleri.getHedefTipi());
        assertThat(result.getSonlandirmami()).isEqualTo(hedefTipleri.getSonlandirmami());
        assertThat(result.getKarsiligi()).isEqualTo(hedefTipleri.getKarsiligi());
        assertThat(result.getSno()).isEqualTo(hedefTipleri.getSno());
        assertThat(result.getHedefTanim()).isEqualTo(hedefTipleri.getHedefTanim());
        assertThat(result.getDurum()).isEqualTo(hedefTipleri.getDurum());
        assertThat(result.getHitapTip()).isEqualTo(hedefTipleri.getHitapTip());
        assertThat(result.getHitapIcerikTip()).isEqualTo(hedefTipleri.getHitapIcerikTip());
        assertThat(result.getHitapIcindemi()).isEqualTo(hedefTipleri.getHitapIcindemi());
        assertThat(result.getHitapEh()).isEqualTo(hedefTipleri.getHitapEh());
        assertThat(result.getMinl()).isEqualTo(hedefTipleri.getMinl());
        assertThat(result.getMaxl()).isEqualTo(hedefTipleri.getMaxl());
        assertThat(result.getImhaYapilsinmi()).isEqualTo(hedefTipleri.getImhaYapilsinmi());
        assertThat(result.getTasinabilirmi()).isEqualTo(hedefTipleri.getTasinabilirmi());
        assertThat(result.getAktifmi()).isEqualTo(hedefTipleri.getAktifmi());
        assertThat(result.getHitapaGonderilecekmi()).isEqualTo(hedefTipleri.getHitapaGonderilecekmi());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HedefTipleriDTO result = hedefTipleriMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        HedefTipleri result = hedefTipleriMapper.toEntity(hedefTipleriDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(hedefTipleriDTO.getId());
        assertThat(result.getHedefKodu()).isEqualTo(hedefTipleriDTO.getHedefKodu());
        assertThat(result.getHedefTipi()).isEqualTo(hedefTipleriDTO.getHedefTipi());
        assertThat(result.getSonlandirmami()).isEqualTo(hedefTipleriDTO.getSonlandirmami());
        assertThat(result.getKarsiligi()).isEqualTo(hedefTipleriDTO.getKarsiligi());
        assertThat(result.getSno()).isEqualTo(hedefTipleriDTO.getSno());
        assertThat(result.getHedefTanim()).isEqualTo(hedefTipleriDTO.getHedefTanim());
        assertThat(result.getDurum()).isEqualTo(hedefTipleriDTO.getDurum());
        assertThat(result.getHitapTip()).isEqualTo(hedefTipleriDTO.getHitapTip());
        assertThat(result.getHitapIcerikTip()).isEqualTo(hedefTipleriDTO.getHitapIcerikTip());
        assertThat(result.getHitapIcindemi()).isEqualTo(hedefTipleriDTO.getHitapIcindemi());
        assertThat(result.getHitapEh()).isEqualTo(hedefTipleriDTO.getHitapEh());
        assertThat(result.getMinl()).isEqualTo(hedefTipleriDTO.getMinl());
        assertThat(result.getMaxl()).isEqualTo(hedefTipleriDTO.getMaxl());
        assertThat(result.getImhaYapilsinmi()).isEqualTo(hedefTipleriDTO.getImhaYapilsinmi());
        assertThat(result.getTasinabilirmi()).isEqualTo(hedefTipleriDTO.getTasinabilirmi());
        assertThat(result.getAktifmi()).isEqualTo(hedefTipleriDTO.getAktifmi());
        assertThat(result.getHitapaGonderilecekmi()).isEqualTo(hedefTipleriDTO.getHitapaGonderilecekmi());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        HedefTipleri result = hedefTipleriMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        HedefTipleri existingEntity = HedefTipleri.builder()
                .id(1L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON")
                .durum("AKTIF")
                .build();

        HedefTipleriDTO updatedDto = HedefTipleriDTO.builder()
                .id(2L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON_UPDATED")
                .durum("PASIF")
                .build();

        // When
        HedefTipleri result = hedefTipleriMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(2L);
        assertThat(result.getHedefKodu()).isEqualTo(101L);
        assertThat(result.getHedefTipi()).isEqualTo("TELEFON_UPDATED");
        assertThat(result.getDurum()).isEqualTo("PASIF");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        HedefTipleri existingEntity = HedefTipleri.builder()
                .id(1L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON")
                .durum("AKTIF")
                .build();

        // When
        HedefTipleri result = hedefTipleriMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HedefTipleri result = hedefTipleriMapper.updateEntityFromDto(null, hedefTipleriDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<HedefTipleri> entityList = Arrays.asList(hedefTipleri, hedefTipleri);

        // When
        List<HedefTipleriDTO> result = hedefTipleriMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getHedefKodu()).isEqualTo(hedefTipleri.getHedefKodu());
        assertThat(result.get(1).getHedefKodu()).isEqualTo(hedefTipleri.getHedefKodu());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<HedefTipleriDTO> result = hedefTipleriMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<HedefTipleriDTO> dtoList = Arrays.asList(hedefTipleriDTO, hedefTipleriDTO);

        // When
        List<HedefTipleri> result = hedefTipleriMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getHedefKodu()).isEqualTo(hedefTipleriDTO.getHedefKodu());
        assertThat(result.get(1).getHedefKodu()).isEqualTo(hedefTipleriDTO.getHedefKodu());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<HedefTipleri> result = hedefTipleriMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
