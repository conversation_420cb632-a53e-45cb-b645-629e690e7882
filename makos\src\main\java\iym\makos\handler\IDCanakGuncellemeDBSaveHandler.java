package iym.makos.handler;

import iym.common.model.api.GuncellemeTip;
import iym.common.model.api.HedefTip;
import iym.common.model.entity.oracle.iym.*;
import iym.common.util.CommonUtils;
import iym.db.jpa.oracle.dao.*;
import iym.makos.dto.id.IDCanakGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.CanakGuncellemeDetay;
import iym.makos.model.api.CanakGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDCanakGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDCanakGuncellemeRequest> {

    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private HedeflerDetayTalepRepo hedeflerDetayTalepRepo;

    @Autowired
    public IDCanakGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , HedeflerDetayTalepRepo hedeflerDetayTalepRepo
            , KararRequestMapper kararRequestMapper) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    @Transactional
    public Long handleDbSave(IDCanakGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            List<CanakGuncellemeKararDetay> guncellemeListesi = request.getCanakGuncellemeKararDetayListesi();
            for (CanakGuncellemeKararDetay canakGuncellemeKarar : guncellemeListesi) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = canakGuncellemeKarar.getMahkemeKararDetay();
                Optional<MahkemeKarar> iliskiliMahkemeKararO = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (iliskiliMahkemeKararO.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                }
                MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararO.get();


                DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                //Her bir hedefin iliskili mahkeme kararda olup olmadigini kontrol et
                List<CanakGuncellemeDetay> canakGuncellemeListesi = canakGuncellemeKarar.getCanakGuncellemeDetayList();
                if (canakGuncellemeListesi != null) {
                    for (CanakGuncellemeDetay canakGuncellemeDetay : canakGuncellemeListesi) {

                        String hedefNo = canakGuncellemeDetay.getCanakHedefDetay().getHedef().getHedefNo();
                        HedefTip hedefTipi = canakGuncellemeDetay.getCanakHedefDetay().getHedef().getHedefTip();
                        String canakNo = canakGuncellemeDetay.getCanakHedefDetay().getCanakHedefNo();

                        Optional<Hedefler> iliskiliHedef = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        if (iliskiliHedef.isEmpty()) {
                            throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        }

                        //TODO : her ihtimale karsi bunu yapmali miyiz?
                        if (canakGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.CIKAR && !CommonUtils.isNullOrEmpty(canakNo)) {
                            throw new MakosResponseException(MakosResponseErrorCodes.CANAKNO_BOS_OLMALIDIR);
                        } else if (canakGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.EKLE && CommonUtils.isNullOrEmpty(canakNo)) {
                            throw new MakosResponseException(MakosResponseErrorCodes.CANAKNO_BOS_OLAMAZ);
                        }

                        //ilgili canak degisikligini HEDEFLER_DETAY_TALEP tablosuna kaydet.
                        HedeflerDetayTalep hedeflerDetayTalep = new HedeflerDetayTalep();
                        hedeflerDetayTalep.setIliskiliHedefId(iliskiliMahkemeKarar.getId());
                        hedeflerDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
                        hedeflerDetayTalep.setHedefNo(hedefNo);
                        hedeflerDetayTalep.setHedefTipi((long) hedefTipi.getHedefKodu());
                        hedeflerDetayTalep.setCanakNo(canakNo);

                        HedeflerDetayTalep savedHedeflerDetayTalep = hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                    }
                }
            }
            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDCanakGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

