package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.EvrakSiraNo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for Gorevler2 entity
 */
@Repository
public interface EvrakSiraNoRepo extends JpaRepository<EvrakSiraNo, Long> {

    Optional<EvrakSiraNo> findByYilAndSiraNo(Long yil, Long siraNo);
    Optional<EvrakSiraNo> findByYil(Long yil);

}
