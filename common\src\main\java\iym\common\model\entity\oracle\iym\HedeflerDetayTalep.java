package iym.common.model.entity.oracle.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for HEDEFLER_DETAY_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HedeflerDetayTalep")
@Table(name = "HEDEFLER_DETAY_TALEP")
public class HedeflerDetayTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HEDEFLER_DETAY_TALEP_SEQ")
    @SequenceGenerator(name = "HEDEFLER_DETAY_TALEP_SEQ", sequenceName = "HEDEFLER_DETAY_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "HEDEF_NO", length = 100)
    @Size(max = 100)
    private String hedefNo;

    @Column(name = "HEDEF_TIPI", nullable = false)
    @NotNull
    private Long hedefTipi;

    @Column(name = "HEDEF_ADI", length = 100)
    @Size(max = 100)
    private String hedefAdi;

    @Column(name = "HEDEF_SOYADI", length = 100)
    @Size(max = 100)
    private String hedefSoyadi;


    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararTalepId;

    @Column(name = "MAHKEME_KARAR_DETAY_ID", nullable = false)
    @NotNull
    private Long mahkemeKararDetayTalepId;

    @Column(name = "ILISKILI_HEDEF_ID", nullable = false)
    @NotNull
    private Long iliskiliHedefId;


    @Column(name = "KAYIT_TARIHI", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date kayitTarihi;


    @Column(name = "DURUMU", length = 15)
    @Size(max = 15)
    private String durumu;

    @Column(name = "CANAK_NO", length = 100)
    @Size(max = 100)
    private String canakNo;

}
