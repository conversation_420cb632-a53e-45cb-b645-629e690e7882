package iym.makos.service.makos;

import iym.common.model.entity.oracle.iym.MahkemeAidiyat;
import iym.common.service.db.DbMahkemeAidiyatService;
import iym.makos.dto.MahkemeAidiyatDTO;
import iym.makos.mapper.MahkemeAidiyatMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeAidiyatTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeAidiyatService {

    private final DbMahkemeAidiyatService dbMahkemeAidiyatService;
    private final MahkemeAidiyatMapper mahkemeAidiyatMapper;

    /**
     * Get mahkeme aidiyat talep records by mahkeme ID
     * @param mahkemeKararId Mahkeme ID
     * @return List of MahkemeAidiyatTalepDTO
     */
    public List<MahkemeAidiyatDTO> findByMahkemeId(Long mahkemeKararId) {
        List<MahkemeAidiyat> mahkemeAidiyatTalepList = dbMahkemeAidiyatService.findByMahkemeKararId(mahkemeKararId);
        return mahkemeAidiyatMapper.toDtoList(mahkemeAidiyatTalepList);
    }


    /**
     * Get mahkeme aidiyat talep by mahkeme ID and aidiyat kod
     * @param mahkemeKararId Mahkeme ID
     * @param aidiyatKod Aidiyat kod
     * @return MahkemeAidiyatTalepDTO
     * @throws ResponseStatusException if not found
     */
    public MahkemeAidiyatDTO findByMahkemeIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod) {
        return dbMahkemeAidiyatService.findByMahkemeKararIdAndAidiyatKod(mahkemeKararId, aidiyatKod)
                .map(mahkemeAidiyatMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat  bulunamadı"));
    }


    /**
     * Create new mahkeme aidiyat talep
     * @param mahkemeAidiyatDTO MahkemeAidiyatTalepDTO
     * @return Created MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatDTO create(MahkemeAidiyatDTO mahkemeAidiyatDTO) {
        // Check if mahkeme aidiyat already exists
        if (mahkemeAidiyatDTO.getMahkemeKararId() != null && mahkemeAidiyatDTO.getAidiyatKod() != null) {
            Optional<MahkemeAidiyat> existingMahkemeAidiyatTalep = dbMahkemeAidiyatService.findByMahkemeKararIdAndAidiyatKod( mahkemeAidiyatDTO.getMahkemeKararId(), mahkemeAidiyatDTO.getAidiyatKod());
                    
            if (existingMahkemeAidiyatTalep.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu mahkeme aidiyat talebi zaten mevcut: Mahkeme ID: " +
                            mahkemeAidiyatDTO.getMahkemeKararId() + ", Aidiyat Kod: " +
                            mahkemeAidiyatDTO.getAidiyatKod());
            }
        }

        MahkemeAidiyat mahkemeAidiyat = mahkemeAidiyatMapper.toEntity(mahkemeAidiyatDTO);
        dbMahkemeAidiyatService.save(mahkemeAidiyat);
        log.info("Mahkeme aidiyat  oluşturuldu: {}", mahkemeAidiyat.getId());
        return mahkemeAidiyatMapper.toDto(mahkemeAidiyat);
    }

    /**
     * Update mahkeme aidiyat talep
     * @param id Mahkeme aidiyat talep ID
     * @param mahkemeAidiyatDTO MahkemeAidiyatTalepDTO
     * @return Updated MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatDTO update(Long id, MahkemeAidiyatDTO mahkemeAidiyatDTO) {
        MahkemeAidiyat existingMahkemeAidiyat = dbMahkemeAidiyatService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat bulunamadı: " + id));

        // Check if mahkeme aidiyat is being changed and already exists
        if (mahkemeAidiyatDTO.getMahkemeKararId() != null &&
                mahkemeAidiyatDTO.getAidiyatKod() != null &&
            (!mahkemeAidiyatDTO.getMahkemeKararId().equals(existingMahkemeAidiyat.getMahkemeKararId()) ||
             !mahkemeAidiyatDTO.getAidiyatKod().equals(existingMahkemeAidiyat.getAidiyatKod()))) {
            
            Optional<MahkemeAidiyat> mahkemeAidiyatTalepWithSameDetails =
                dbMahkemeAidiyatService.findByMahkemeKararIdAndAidiyatKod(
                        mahkemeAidiyatDTO.getMahkemeKararId(),
                        mahkemeAidiyatDTO.getAidiyatKod());
                    
            if (mahkemeAidiyatTalepWithSameDetails.isPresent() && 
                !mahkemeAidiyatTalepWithSameDetails.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu mahkeme aidiyat  zaten mevcut: Mahkeme ID: " +
                            mahkemeAidiyatDTO.getMahkemeKararId() + ", Aidiyat Kod: " +
                            mahkemeAidiyatDTO.getAidiyatKod());
            }
        }

        MahkemeAidiyat updatedMahkemeAidiyat = mahkemeAidiyatMapper.updateEntityFromDto(existingMahkemeAidiyat, mahkemeAidiyatDTO);
        dbMahkemeAidiyatService.update(updatedMahkemeAidiyat);
        log.info("Mahkeme aidiyat  güncellendi: {}", updatedMahkemeAidiyat.getId());
        return mahkemeAidiyatMapper.toDto(updatedMahkemeAidiyat);
    }

    /**
     * Delete mahkeme aidiyat talep
     * @param id Mahkeme aidiyat talep ID
     */
    public void delete(Long id) {
        MahkemeAidiyat mahkemeAidiyatTalep = dbMahkemeAidiyatService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat  bulunamadı: " + id));
        
        dbMahkemeAidiyatService.delete(mahkemeAidiyatTalep);
        log.info("Mahkeme aidiyat  silindi: {}", id);
    }
}
