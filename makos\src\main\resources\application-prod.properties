# Production Environment Configuration for Makos
# Oracle Database configuration for production
spring.datasource.oracle.url=jdbc:oracle:thin:@${DB_HOST:localhost}:${DB_PORT:1521}/${DB_SERVICE:XE}
spring.datasource.oracle.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=${DB_USERNAME:iym}
spring.datasource.oracle.password=${DB_PASSWORD:iym}
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.properties.hibernate.default_schema=${DB_SCHEMA:iym}

# JPA configuration for production
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.hbm2ddl.auto=none

# Production specific logging (minimal)
logging.level.root=WARN
logging.level.iym=INFO
logging.level.org.hibernate=WARN
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Connection pool configuration for production
spring.datasource.oracle.hikari.connectionTimeout=30000
spring.datasource.oracle.hikari.maximumPoolSize=20
spring.datasource.oracle.hikari.minimumIdle=5
spring.datasource.oracle.hikari.idleTimeout=300000
spring.datasource.oracle.hikari.maxLifetime=1200000

# Application specific properties for production
app.init-db=false

# CORS Configuration for production (must be set via environment variables)
cors.allowed.origins=${CORS_ALLOWED_ORIGINS}

# Security settings for production
server.error.include-stacktrace=never
server.error.include-message=never
