package iym.makos.validation;

import iym.common.model.api.Evrak<PERSON>uru<PERSON>;
import iym.common.model.api.HedefTip;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.model.entity.oracle.iym.Hedefler;
import iym.common.model.entity.oracle.iym.MahkemeKarar;
import iym.common.model.entity.oracle.iym.SucTipi;
import iym.common.service.db.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDUzatmaKarariRequest;
import iym.makos.model.api.HedefDetayID;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Optional;

@Service
@Slf4j
public class IDUzatmaKarariValidator extends MahkemeKararRequestValidatorBase<IDUzatmaKarariRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public IDUzatmaKarariValidator(DbMahkemeKararService dbMahkemeKararService,
                                   DbHedeflerService dbHedeflerService,
                                   DbSucTipiService dbSucTipiService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    public ValidationResult validate(IDUzatmaKarariRequest request) {

        try {

            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);
            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();

            //Ohal Kontrolu
            if (LocalDate.now().isAfter(IDYeniKararValidator.OHAL_DATE) && kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)) {
                validationResult.addFailedReason("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
            }

            for (HedefDetayID hedefDetayID : request.getHedefDetayListesi()) {

                if (!CommonUtils.isNullOrEmpty(hedefDetayID.getCanakNo())) {
                    validationResult.addFailedReason("Uzatma kararında CANAK numarası girilemez.");
                }

                if (hedefDetayID.getUzatmaSayisi() == null) {
                    validationResult.addFailedReason("Uzatma Kararinda uzatma sayisi boş olamaz!");
                }

                MahkemeKararDetay iliskiliMahkemeKararDetay = hedefDetayID.getIlgiliMahkemeKararDetayi();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.!");
                } else {

                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format("Güncelle Konu Mahkeme Karar Bulunamadı: Mahheme İl/İlçe Kodu: %s Mahkeme Kodu: %s, Karar No: %s, Soruşturma No :%s"
                                , iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {
                        String hedefNo = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefNo();
                        HedefTip hedefTipi = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefTip();

                        //TODO: sonlandirmada daha fazla veya daha az hedef varsa?
                        //Eski karar uzatmaya uygun olup olmadigi burada mi kontrol edilecek
                        Optional<Hedefler> existingHedefBilgisiOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKararOpt.get().getId(), hedefNo, hedefTipi);

                        if (existingHedefBilgisiOpt.isEmpty()) {
                            validationResult.addFailedReason(hedefNo + " numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
                        }
                    }
                }
            }

            Optional.ofNullable(request.getMahkemeSucTipiKodlari())
                    .orElse(Collections.emptyList())
                    .forEach(sucTipiKodu -> {
                        Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                        if (sucTipiOpt.isEmpty()) {
                            validationResult.addFailedReason(String.format( "'%s' kodu ile eşleşen bir suç tipi mevcut değil.", sucTipiKodu));
                        }
                    });


            Optional.ofNullable(request.getMahkemeAidiyatKodlari())
                    .orElse(Collections.emptyList())
                    .forEach(aidiyatKod -> {
                        String uyariMesaj = CommonUtils.aidiyatEklemeUyarisi(aidiyatKod, kararTipi, evrakKurum);
                        if (!CommonUtils.isNullOrEmpty(uyariMesaj)) {
                            validationResult.addFailedReason(uyariMesaj);
                        }
                    });

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI;
    }

}

