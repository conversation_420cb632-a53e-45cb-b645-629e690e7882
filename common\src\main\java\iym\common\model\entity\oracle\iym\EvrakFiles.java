package iym.common.model.entity.oracle.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for HEDEFLER_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "EvrakFiles")
@Table(name = "EVRAK_FILES")
public class EvrakFiles implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EVRAK_FILES_SEQ")
    @SequenceGenerator(name = "EVRAK_FILES_SEQ", sequenceName = "EVRAK_FILES_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "FILE_NAME", nullable = false, length = 400)
    @NotNull
    @Size(max = 400)
    private String fileName;

    @Column(name = "SIRA_NO")
    @NotNull
    private Long siraNo;

    @Column(name = "SILINDI", length = 1)
    @Size(max = 1)
    private String silindi;
}
