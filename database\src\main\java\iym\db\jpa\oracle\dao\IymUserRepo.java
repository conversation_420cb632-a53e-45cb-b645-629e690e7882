package iym.db.jpa.oracle.dao;


import iym.common.model.entity.oracle.iym.IymUser;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
public interface IymUserRepo extends JpaRepository<IymUser, UUID> {

    Optional<IymUser> findByUsername(String name);

    List<IymUser> findByStatus(UserStatusType status);

    List<IymUser> findByRoleOrderByUsernameAsc(IymUserRoleType role);

    List<IymUser> findAllByOrderByUsernameAsc();

}
