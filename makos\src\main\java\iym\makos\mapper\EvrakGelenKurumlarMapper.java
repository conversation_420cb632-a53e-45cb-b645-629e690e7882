package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.EvrakGelenKurumlar;
import org.springframework.stereotype.Component;
import iym.makos.dto.EvrakGelenKurumlarDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for EvrakGelenKurumlar entity and DTO
 */
@Component
public class EvrakGelenKurumlarMapper {

    /**
     * Convert entity to DTO
     * @param entity EvrakGelenKurumlar entity
     * @return EvrakGelenKurumlarDTO
     */
    public EvrakGelenKurumlarDTO toDto(EvrakGelenKurumlar entity) {
        if (entity == null) {
            return null;
        }

        return EvrakGelenKurumlarDTO.builder()
                .id(entity.getId())
                .kurumKod(entity.getKurumKod())
                .kurumAdi(entity.getKurumAdi())
                .kurum(entity.getKurum())
                .idx(entity.getIdx())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto EvrakGelenKurumlarDTO
     * @return EvrakGelenKurumlar entity
     */
    public EvrakGelenKurumlar toEntity(EvrakGelenKurumlarDTO dto) {
        if (dto == null) {
            return null;
        }

        return EvrakGelenKurumlar.builder()
                .id(dto.getId())
                .kurumKod(dto.getKurumKod())
                .kurumAdi(dto.getKurumAdi())
                .kurum(dto.getKurum())
                .idx(dto.getIdx())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity EvrakGelenKurumlar entity to update
     * @param dto EvrakGelenKurumlarDTO with new values
     * @return Updated EvrakGelenKurumlar entity
     */
    public EvrakGelenKurumlar updateEntityFromDto(EvrakGelenKurumlar entity, EvrakGelenKurumlarDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        // ID should not be updated
        entity.setKurumKod(dto.getKurumKod());
        entity.setKurumAdi(dto.getKurumAdi());
        entity.setKurum(dto.getKurum());
        entity.setIdx(dto.getIdx());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of EvrakGelenKurumlar entities
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> toDtoList(List<EvrakGelenKurumlar> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of EvrakGelenKurumlarDTO
     * @return List of EvrakGelenKurumlar entities
     */
    public List<EvrakGelenKurumlar> toEntityList(List<EvrakGelenKurumlarDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
