package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.MahkemeAidiyatTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.MahkemeAidiyatTalepDTO;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class MahkemeAidiyatTalepMapperTest {

    private MahkemeAidiyatTalepMapper mahkemeAidiyatTalepMapper;
    private MahkemeAidiyatTalep mahkemeAidiyatTalep;
    private MahkemeAidiyatTalepDTO mahkemeAidiyatTalepDTO;

    @BeforeEach
    void setUp() {
        mahkemeAidiyatTalepMapper = new MahkemeAidiyatTalepMapper();

        mahkemeAidiyatTalep = MahkemeAidiyatTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .aidiyatKod("02")
                .durumu("AKTIF")
                .build();

        mahkemeAidiyatTalepDTO = MahkemeAidiyatTalepDTO.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .aidiyatKod("02")
                .durumu("AKTIF")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        MahkemeAidiyatTalepDTO result = mahkemeAidiyatTalepMapper.toDto(mahkemeAidiyatTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeAidiyatTalep.getId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(mahkemeAidiyatTalep.getMahkemeKararTalepId());
        assertThat(result.getAidiyatKod()).isEqualTo(mahkemeAidiyatTalep.getAidiyatKod());
        assertThat(result.getDurumu()).isEqualTo(mahkemeAidiyatTalep.getDurumu());
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        MahkemeAidiyatTalep result = mahkemeAidiyatTalepMapper.toEntity(mahkemeAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeAidiyatTalepDTO.getId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(mahkemeAidiyatTalepDTO.getMahkemeKararTalepId());
        assertThat(result.getAidiyatKod()).isEqualTo(mahkemeAidiyatTalepDTO.getAidiyatKod());
        assertThat(result.getDurumu()).isEqualTo(mahkemeAidiyatTalepDTO.getDurumu());
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        MahkemeAidiyatTalep existingEntity = MahkemeAidiyatTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .aidiyatKod("02")
                .durumu("AKTIF")
                .build();

        MahkemeAidiyatTalepDTO updatedDto = MahkemeAidiyatTalepDTO.builder()
                .id(1L)
                .mahkemeKararTalepId(101L)
                .aidiyatKod("03")
                .durumu("PASIF")
                .build();

        // When
        MahkemeAidiyatTalep result = mahkemeAidiyatTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(existingEntity.getId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(updatedDto.getMahkemeKararTalepId());
        assertThat(result.getAidiyatKod()).isEqualTo(updatedDto.getAidiyatKod());
        assertThat(result.getDurumu()).isEqualTo(updatedDto.getDurumu());
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<MahkemeAidiyatTalep> entityList = Arrays.asList(
                mahkemeAidiyatTalep,
                MahkemeAidiyatTalep.builder()
                        .id(2L)
                        .mahkemeKararTalepId(102L)
                        .aidiyatKod("03")
                        .durumu("AKTIF")
                        .build()
        );

        // When
        List<MahkemeAidiyatTalepDTO> result = mahkemeAidiyatTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(entityList.get(0).getId());
        assertThat(result.get(1).getId()).isEqualTo(entityList.get(1).getId());
        assertThat(result.get(0).getAidiyatKod()).isEqualTo(entityList.get(0).getAidiyatKod());
        assertThat(result.get(1).getAidiyatKod()).isEqualTo(entityList.get(1).getAidiyatKod());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeAidiyatTalepDTO result = mahkemeAidiyatTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        MahkemeAidiyatTalep result = mahkemeAidiyatTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldReturnEmptyListWhenEntityListIsNull() {
        // When
        List<MahkemeAidiyatTalepDTO> result = mahkemeAidiyatTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }
}
