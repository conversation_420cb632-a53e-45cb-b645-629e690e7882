package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.MahkemeKararTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class MahkemeKararTalepMapperTest {

    private MahkemeKararTalepMapper mahkemeKararTalepMapper;
    private MahkemeKararTalep mahkemeKararTalep;
    private MahkemeKararTalepDTO mahkemeKararTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        mahkemeKararTalepMapper = new MahkemeKararTalepMapper();
        testDate = new Date();

        mahkemeKararTalep = MahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahKararBasTar(testDate)
                .mahKararBitisTar(testDate)
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeKararNo("2023/123")
                .mahkemeIlIlceKodu("0600")
                .aciklama("Test açıklama")
                .hakimSicilNo("12345")
                .sorusturmaNo("2023/456")
                .gercekMahId(300L)
                .mahkemeKodu("SC01")
                .build();

        mahkemeKararTalepDTO = MahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahKararBasTar(testDate)
                .mahKararBitisTar(testDate)
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeKararNo("2023/123")
                .mahkemeIlIlceKodu("0600")
                .aciklama("Test açıklama")
                .hakimSicilNo("12345")
                .sorusturmaNo("2023/456")
                .gercekMahId(300L)
                .mahkemeKodu("SC01")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        MahkemeKararTalepDTO result = mahkemeKararTalepMapper.toDto(mahkemeKararTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeKararTalep.getId());
        assertThat(result.getEvrakId()).isEqualTo(mahkemeKararTalep.getEvrakId());
        assertThat(result.getKullaniciId()).isEqualTo(mahkemeKararTalep.getKullaniciId());
        assertThat(result.getKayitTarihi()).isEqualTo(mahkemeKararTalep.getKayitTarihi());
        assertThat(result.getDurum()).isEqualTo(mahkemeKararTalep.getDurum());
        assertThat(result.getHukukBirim()).isEqualTo(mahkemeKararTalep.getHukukBirim());
        assertThat(result.getKararTip()).isEqualTo(mahkemeKararTalep.getKararTip());
        assertThat(result.getMahKararBasTar()).isEqualTo(mahkemeKararTalep.getMahKararBasTar());
        assertThat(result.getMahKararBitisTar()).isEqualTo(mahkemeKararTalep.getMahKararBitisTar());
        assertThat(result.getMahkemeAdi()).isEqualTo(mahkemeKararTalep.getMahkemeAdi());
        assertThat(result.getMahkemeKararNo()).isEqualTo(mahkemeKararTalep.getMahkemeKararNo());
        assertThat(result.getMahkemeIlIlceKodu()).isEqualTo(mahkemeKararTalep.getMahkemeIlIlceKodu());
        assertThat(result.getAciklama()).isEqualTo(mahkemeKararTalep.getAciklama());
        assertThat(result.getHakimSicilNo()).isEqualTo(mahkemeKararTalep.getHakimSicilNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(mahkemeKararTalep.getSorusturmaNo());
        assertThat(result.getGercekMahId()).isEqualTo(mahkemeKararTalep.getGercekMahId());
        assertThat(result.getMahkemeKodu()).isEqualTo(mahkemeKararTalep.getMahkemeKodu());
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        MahkemeKararTalep result = mahkemeKararTalepMapper.toEntity(mahkemeKararTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeKararTalepDTO.getId());
        assertThat(result.getEvrakId()).isEqualTo(mahkemeKararTalepDTO.getEvrakId());
        assertThat(result.getKullaniciId()).isEqualTo(mahkemeKararTalepDTO.getKullaniciId());
        assertThat(result.getKayitTarihi()).isEqualTo(mahkemeKararTalepDTO.getKayitTarihi());
        assertThat(result.getDurum()).isEqualTo(mahkemeKararTalepDTO.getDurum());
        assertThat(result.getHukukBirim()).isEqualTo(mahkemeKararTalepDTO.getHukukBirim());
        assertThat(result.getKararTip()).isEqualTo(mahkemeKararTalepDTO.getKararTip());
        assertThat(result.getMahKararBasTar()).isEqualTo(mahkemeKararTalepDTO.getMahKararBasTar());
        assertThat(result.getMahKararBitisTar()).isEqualTo(mahkemeKararTalepDTO.getMahKararBitisTar());
        assertThat(result.getMahkemeAdi()).isEqualTo(mahkemeKararTalepDTO.getMahkemeAdi());
        assertThat(result.getMahkemeKararNo()).isEqualTo(mahkemeKararTalepDTO.getMahkemeKararNo());
        assertThat(result.getMahkemeIlIlceKodu()).isEqualTo(mahkemeKararTalepDTO.getMahkemeIlIlceKodu());
        assertThat(result.getAciklama()).isEqualTo(mahkemeKararTalepDTO.getAciklama());
        assertThat(result.getHakimSicilNo()).isEqualTo(mahkemeKararTalepDTO.getHakimSicilNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(mahkemeKararTalepDTO.getSorusturmaNo());
        assertThat(result.getGercekMahId()).isEqualTo(mahkemeKararTalepDTO.getGercekMahId());
        assertThat(result.getMahkemeKodu()).isEqualTo(mahkemeKararTalepDTO.getMahkemeKodu());
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        MahkemeKararTalep existingEntity = MahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeKararNo("2023/123")
                .mahkemeIlIlceKodu("0600")
                .build();

        MahkemeKararTalepDTO updatedDto = MahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(101L)
                .kullaniciId(201L)
                .kayitTarihi(testDate)
                .durum("PASIF")
                .hukukBirim("AGIR CEZA")
                .kararTip("400")
                .mahkemeAdi("ANKARA 2. AGIR CEZA MAHKEMESİ")
                .mahkemeKararNo("2023/456")
                .mahkemeIlIlceKodu("0600")
                .aciklama("Güncellenmiş açıklama")
                .hakimSicilNo("54321")
                .sorusturmaNo("2023/789")
                .gercekMahId(301L)
                .mahkemeKodu("AC02")
                .build();

        // When
        MahkemeKararTalep result = mahkemeKararTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(existingEntity.getId());
        assertThat(result.getEvrakId()).isEqualTo(updatedDto.getEvrakId());
        assertThat(result.getKullaniciId()).isEqualTo(updatedDto.getKullaniciId());
        assertThat(result.getKayitTarihi()).isEqualTo(updatedDto.getKayitTarihi());
        assertThat(result.getDurum()).isEqualTo(updatedDto.getDurum());
        assertThat(result.getHukukBirim()).isEqualTo(updatedDto.getHukukBirim());
        assertThat(result.getKararTip()).isEqualTo(updatedDto.getKararTip());
        assertThat(result.getMahkemeAdi()).isEqualTo(updatedDto.getMahkemeAdi());
        assertThat(result.getMahkemeKararNo()).isEqualTo(updatedDto.getMahkemeKararNo());
        assertThat(result.getMahkemeIlIlceKodu()).isEqualTo(updatedDto.getMahkemeIlIlceKodu());
        assertThat(result.getAciklama()).isEqualTo(updatedDto.getAciklama());
        assertThat(result.getHakimSicilNo()).isEqualTo(updatedDto.getHakimSicilNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(updatedDto.getSorusturmaNo());
        assertThat(result.getGercekMahId()).isEqualTo(updatedDto.getGercekMahId());
        assertThat(result.getMahkemeKodu()).isEqualTo(updatedDto.getMahkemeKodu());
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<MahkemeKararTalep> entityList = Arrays.asList(
                mahkemeKararTalep,
                MahkemeKararTalep.builder()
                        .id(2L)
                        .evrakId(102L)
                        .kullaniciId(202L)
                        .kayitTarihi(testDate)
                        .durum("AKTIF")
                        .mahkemeAdi("ISTANBUL 1. SULH CEZA HAKİMLİĞİ")
                        .mahkemeKararNo("2023/789")
                        .mahkemeIlIlceKodu("3400")
                        .build()
        );

        // When
        List<MahkemeKararTalepDTO> result = mahkemeKararTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(entityList.get(0).getId());
        assertThat(result.get(1).getId()).isEqualTo(entityList.get(1).getId());
        assertThat(result.get(0).getMahkemeAdi()).isEqualTo(entityList.get(0).getMahkemeAdi());
        assertThat(result.get(1).getMahkemeAdi()).isEqualTo(entityList.get(1).getMahkemeAdi());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeKararTalepDTO result = mahkemeKararTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        MahkemeKararTalep result = mahkemeKararTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldReturnEmptyListWhenEntityListIsNull() {
        // When
        List<MahkemeKararTalepDTO> result = mahkemeKararTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }
}
