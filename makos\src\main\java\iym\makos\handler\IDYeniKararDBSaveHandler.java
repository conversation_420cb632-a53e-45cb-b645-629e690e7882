package iym.makos.handler;

import iym.common.model.entity.oracle.iym.*;
import iym.common.util.CommonUtils;
import iym.db.jpa.oracle.dao.*;
import iym.makos.dto.id.IDYeniKararRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.HedefDetayID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDYeniKararDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDYeniKararRequest> {

    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;
    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @Autowired
    public IDYeniKararDBSaveHandler(HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo
            , MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo) {
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.mahkemeAidiyatTalepRepo = mahkemeAidiyatTalepRepo;
        this.mahkemeSuclarTalepRepo = mahkemeSuclarTalepRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
    }

    @Override
    @Transactional
    public Long handleDbSave(IDYeniKararRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            List<MahkemeAidiyatTalep> savedMahkemeAidiyatList = saveMahkemeKararTalepAidiyatListesi(request.getMahkemeAidiyatKodlari(), mahkemeKararTalepId);

            List<MahkemeSuclarTalep> savedMahkemeSucList = saveMahkemeKararTalepSucKoduListesi(request.getMahkemeSucTipiKodlari(), mahkemeKararTalepId);

            for (HedefDetayID hedefDetayID : request.getHedefDetayListesi()) {
                HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(hedefDetayID, mahkemeKararTalepId, kullaniciId, kayitTarihi);
                HedeflerTalep savedHedeflerTalep = hedeflerTalepRepo.save(hedeflerTalepEntity);
                //Hedef Aidiyat Talepleri Kaydet
                if (hedefDetayID.getHedefAidiyatKodlari() != null) {
                    List<HedeflerAidiyatTalep> savedHedefAidiyatList = saveHedeflerTalepSucKoduListesi(hedefDetayID.getHedefAidiyatKodlari(), savedHedeflerTalep.getId(),
                            savedHedeflerTalep.getHedefNo(), kayitTarihi, kullaniciId);
                }
            }
            return mahkemeKararTalepId;
        }catch(Exception ex){
            log.error("IDYeniKarar handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }


    private List<HedeflerAidiyatTalep> saveHedeflerTalepSucKoduListesi(List<String> sucKoduListesi, Long
            hedeflerTalepId, String hedefNo, Date islemTarihi, Long kullaniciId) throws Exception {
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : sucKoduListesi) {
            HedeflerAidiyatTalep hedeflerAidiyatTalep = kararRequestMapper.toHedeflerAidiyatTalep(kullaniciId, hedeflerTalepId, aidiyatKodu, islemTarihi);
            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = hedeflerAidiyatTalepRepo.save(hedeflerAidiyatTalep);
            result.add(savedHedeflerAidiyatTalep);
        }
        return result;
    }


    private List<MahkemeAidiyatTalep> saveMahkemeKararTalepAidiyatListesi(List<String> aidiyatListesi, Long mahkemeKararTalepId) throws Exception {
        List<MahkemeAidiyatTalep> result = new ArrayList<>();
        //TODO: durum = null olabilmeli
        for (String aidiyatKodu :  CommonUtils.safeList(aidiyatListesi)) {
            MahkemeAidiyatTalep mahkemeAidiyatTalep = kararRequestMapper.toMahkemeAidiyatTalep(mahkemeKararTalepId, aidiyatKodu, null);
            MahkemeAidiyatTalep savedMahkemeAidiyatTalep = mahkemeAidiyatTalepRepo.save(mahkemeAidiyatTalep);
            result.add(savedMahkemeAidiyatTalep);
        }

        return result;
    }

    private List<MahkemeSuclarTalep> saveMahkemeKararTalepSucKoduListesi(List<String> sucKoduListesi, Long mahkemeKararTalepId) throws Exception {
        List<MahkemeSuclarTalep> result = new ArrayList<>();
        for (String sucTipiKodu : CommonUtils.safeList(sucKoduListesi)) {
            MahkemeSuclarTalep mahkemeSuclarTalep = kararRequestMapper.toMahkemeSuclarTalep(mahkemeKararTalepId, sucTipiKodu, null);
            MahkemeSuclarTalep savedMahkemeSuclarTalep = mahkemeSuclarTalepRepo.save(mahkemeSuclarTalep);
            result.add(savedMahkemeSuclarTalep);
        }
        return result;
    }


}

