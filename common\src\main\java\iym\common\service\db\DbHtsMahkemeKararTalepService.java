package iym.common.service.db;

import iym.common.model.entity.oracle.iym.HtsMahkemeKararTalep;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for HtsMahkemeKararTalep entity
 */
public interface DbHtsMahkemeKararTalepService extends GenericDbService<HtsMahkemeKararTalep, Long> {

    List<HtsMahkemeKararTalep> findByEvrakId(Long evrakId);
    
    List<HtsMahkemeKararTalep> findByKullaniciId(Long kullaniciId);
    
    List<HtsMahkemeKararTalep> findByDurum(String durum);
    
    List<HtsMahkemeKararTalep> findByKararTip(String kararTip);
    
    List<HtsMahkemeKararTalep> findByHukukBirim(String hukukBirim);
    
    List<HtsMahkemeKararTalep> findByMahkemeIli(String mahkemeIli);
    
    List<HtsMahkemeKararTalep> findByMahkemeKodu(String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByMahkemeAdiContainingIgnoreCase(String mahkemeAdi);
    
    Optional<HtsMahkemeKararTalep> findByMahkemeKararNo(String mahkemeKararNo);
    
    List<HtsMahkemeKararTalep> findBySorusturmaNo(String sorusturmaNo);
    
    List<HtsMahkemeKararTalep> findByKayitTarihiBetween(Date startDate, Date endDate);
    
    List<HtsMahkemeKararTalep> findByMahkemeIliAndMahkemeKodu(String mahkemeIli, String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByKararTipAndDurum(String kararTip, String durum);
    
    boolean existsByMahkemeKararNo(String mahkemeKararNo);
}
