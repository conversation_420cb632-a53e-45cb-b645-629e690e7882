package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.Hedefler;
import iym.makos.dto.HedeflerDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Hedefler entity and DTO
 */
@Component
public class HedeflerMapper {

    /**
     * Convert entity to DTO
     * @param entity Hedefler entity
     * @return HedeflerDTO
     */
    public HedeflerDTO toDto(Hedefler entity) {
        if (entity == null) {
            return null;
        }

        return HedeflerDTO.builder()
                .id(entity.getId())
                .birimKod(entity.getBirimKod())
                .kullaniciId(entity.getKullaniciId())
                .tekMasaKulId(entity.getTekMasaKulId())
                .hedefNo(entity.getHedefNo())
                .hedefAdi(entity.getHedefAdi())
                .hedefSoyadi(entity.getHedefSoyadi())
                .baslamaTarihi(entity.getBaslamaTarihi())
                .suresi(entity.getSuresi())
                .sureTipi(entity.getSureTipi())
                .uzatmaSayisi(entity.getUzatmaSayisi())
                .durumu(entity.getDurumu())
                .aciklama(entity.getAciklama())
                .mahkemeKararId(entity.getMahkemeKararId())
                .hedefAidiyatId(entity.getHedefAidiyatId())
                .grupKod(entity.getGrupKod())
                .aidiyatKod(entity.getAidiyatKod())
                .uniqKod(entity.getUniqKod())
                .kayitTarihi(entity.getKayitTarihi())
                .tanimlamaTarihi(entity.getTanimlamaTarihi())
                .kapatmaKararId(entity.getKapatmaKararId())
                .kapatmaTarihi(entity.getKapatmaTarihi())
                .imha(entity.getImha())
                .imhaTarihi(entity.getImhaTarihi())
                .uzatmaId(entity.getUzatmaId())
                .acilmi(entity.getAcilmi())
                .hedef118Adi(entity.getHedef118Adi())
                .hedef118Soyadi(entity.getHedef118Soyadi())
                .hedef118Adres(entity.getHedef118Adres())
                .hedefTipi(entity.getHedefTipi())
                .canakNo(entity.getCanakNo())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto HedeflerDTO
     * @return Hedefler entity
     */
    public Hedefler toEntity(HedeflerDTO dto) {
        if (dto == null) {
            return null;
        }

        return Hedefler.builder()
                .id(dto.getId())
                .birimKod(dto.getBirimKod())
                .kullaniciId(dto.getKullaniciId())
                .tekMasaKulId(dto.getTekMasaKulId())
                .hedefNo(dto.getHedefNo())
                .hedefAdi(dto.getHedefAdi())
                .hedefSoyadi(dto.getHedefSoyadi())
                .baslamaTarihi(dto.getBaslamaTarihi())
                .suresi(dto.getSuresi())
                .sureTipi(dto.getSureTipi())
                .uzatmaSayisi(dto.getUzatmaSayisi())
                .durumu(dto.getDurumu())
                .aciklama(dto.getAciklama())
                .mahkemeKararId(dto.getMahkemeKararId())
                .hedefAidiyatId(dto.getHedefAidiyatId())
                .grupKod(dto.getGrupKod())
                .aidiyatKod(dto.getAidiyatKod())
                .uniqKod(dto.getUniqKod())
                .kayitTarihi(dto.getKayitTarihi())
                .tanimlamaTarihi(dto.getTanimlamaTarihi())
                .kapatmaKararId(dto.getKapatmaKararId())
                .kapatmaTarihi(dto.getKapatmaTarihi())
                .imha(dto.getImha())
                .imhaTarihi(dto.getImhaTarihi())
                .uzatmaId(dto.getUzatmaId())
                .acilmi(dto.getAcilmi())
                .hedef118Adi(dto.getHedef118Adi())
                .hedef118Soyadi(dto.getHedef118Soyadi())
                .hedef118Adres(dto.getHedef118Adres())
                .hedefTipi(dto.getHedefTipi())
                .canakNo(dto.getCanakNo())
                .build();
    }



    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of Hedefler entities
     * @return List of HedeflerDTO
     */
    public List<HedeflerDTO> toDtoList(List<Hedefler> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of HedeflerDTO
     * @return List of Hedefler entities
     */
    public List<Hedefler> toEntityList(List<HedeflerDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
