package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.HtsMahkemeKararTalep;
import org.springframework.stereotype.Component;
import iym.makos.dto.HtsMahkemeKararTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for HtsMahkemeKararTalep entity and DTO
 */
@Component
public class HtsMahkemeKararTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity HtsMahkemeKararTalep entity
     * @return HtsMahkemeKararTalepDTO
     */
    public HtsMahkemeKararTalepDTO toDto(HtsMahkemeKararTalep entity) {
        if (entity == null) {
            return null;
        }

        return HtsMahkemeKararTalepDTO.builder()
                .id(entity.getId())
                .evrakId(entity.getEvrakId())
                .kullaniciId(entity.getKullaniciId())
                .kayitTarihi(entity.getKayitTarihi())
                .durum(entity.getDurum())
                .kararTip(entity.getKararTip())
                .hukukBirim(entity.getHukukBirim())
                .mahkemeIli(entity.getMahkemeIli())
                .mahkemeKodu(entity.getMahkemeKodu())
                .mahkemeAdi(entity.getMahkemeAdi())
                .aciklama(entity.getAciklama())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto HtsMahkemeKararTalepDTO
     * @return HtsMahkemeKararTalep entity
     */
    public HtsMahkemeKararTalep toEntity(HtsMahkemeKararTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return HtsMahkemeKararTalep.builder()
                .id(dto.getId())
                .evrakId(dto.getEvrakId())
                .kullaniciId(dto.getKullaniciId())
                .kayitTarihi(dto.getKayitTarihi())
                .durum(dto.getDurum())
                .kararTip(dto.getKararTip())
                .hukukBirim(dto.getHukukBirim())
                .mahkemeIli(dto.getMahkemeIli())
                .mahkemeKodu(dto.getMahkemeKodu())
                .mahkemeAdi(dto.getMahkemeAdi())
                .aciklama(dto.getAciklama())
                .mahkemeKararNo(dto.getMahkemeKararNo())
                .sorusturmaNo(dto.getSorusturmaNo())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity HtsMahkemeKararTalep entity to update
     * @param dto HtsMahkemeKararTalepDTO with new values
     * @return Updated HtsMahkemeKararTalep entity
     */
    public HtsMahkemeKararTalep updateEntityFromDto(HtsMahkemeKararTalep entity, HtsMahkemeKararTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        // evrakId is a foreign key and should not be updated
        // kullaniciId is a foreign key and should not be updated
        entity.setKayitTarihi(dto.getKayitTarihi());
        entity.setDurum(dto.getDurum());
        entity.setKararTip(dto.getKararTip());
        entity.setHukukBirim(dto.getHukukBirim());
        entity.setMahkemeIli(dto.getMahkemeIli());
        entity.setMahkemeKodu(dto.getMahkemeKodu());
        entity.setMahkemeAdi(dto.getMahkemeAdi());
        entity.setAciklama(dto.getAciklama());
        entity.setMahkemeKararNo(dto.getMahkemeKararNo());
        entity.setSorusturmaNo(dto.getSorusturmaNo());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of HtsMahkemeKararTalep entities
     * @return List of HtsMahkemeKararTalepDTO
     */
    public List<HtsMahkemeKararTalepDTO> toDtoList(List<HtsMahkemeKararTalep> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of HtsMahkemeKararTalepDTO
     * @return List of HtsMahkemeKararTalep entities
     */
    public List<HtsMahkemeKararTalep> toEntityList(List<HtsMahkemeKararTalepDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
