# Test Environment Configuration for Makos
# H2 In-Memory Database for testing
spring.datasource.oracle.url=jdbc:h2:mem:makostestdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.oracle.driverClassName=org.h2.Driver
spring.datasource.oracle.username=sa
spring.datasource.oracle.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2 Console for debugging tests (if needed)
spring.h2.console.enabled=true

# JPA configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop

# Connection pool configuration for tests
spring.datasource.oracle.hikari.connectionTimeout=20000
spring.datasource.oracle.hikari.maximumPoolSize=5

# Logging configuration for tests
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=INFO
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Application specific properties for tests
app.init-db=false

# Swagger configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true
