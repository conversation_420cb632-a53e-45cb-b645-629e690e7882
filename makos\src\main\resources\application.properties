# Server configuration
server.port=5000
server.servlet.context-path=/makosapi

## to avoid exception on startup: NoSuchFileException: mchange-commons-java-0.2.19.jar
server.tomcat.additional-tld-skip-patterns=*mchange-commons-java*.jar

# Default profile (development)
spring.profiles.active=dev

# =============================================================================
# MULTI-DATASOURCE CONFIGURATION
# =============================================================================
# Disable Spring Boot's default DataSource auto-configuration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration

# JPA configuration (common for all profiles)
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Connection pool configuration (common for all profiles)
spring.datasource.oracle.hikari.connectionTimeout=20000
spring.datasource.oracle.hikari.maximumPoolSize=5

# Logging configuration
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari=DEBUG

# Swagger configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true

# Application specific properties
app.init-db=true

# CORS Configuration
cors.allowed.origins=${CORS_ALLOWED_ORIGINS:*}
