package iym.makos.dto.user;

import iym.common.model.api.ApiResponseBase;
import iym.common.model.entity.oracle.makos.MakosUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * Get user by ID response DTO for MAKOS user management
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class GetUserByIdResponse extends ApiResponseBase {
    private MakosUser user;
} 