package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.CanakNumaralar;
import org.springframework.stereotype.Component;
import iym.makos.dto.CanakNumaralarDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for CanakNumaralar entity and DTO
 */
@Component
public class CanakNumaralarMapper {

    /**
     * Convert entity to DTO
     * @param entity CanakNumaralar entity
     * @return CanakNumaralarDTO
     */
    public CanakNumaralarDTO toDto(CanakNumaralar entity) {
        if (entity == null) {
            return null;
        }

        return CanakNumaralarDTO.builder()
                .id(entity.getId())
                .canakNo(entity.getCanakNo())
                .kurumKod(entity.getKurumKod())
                .eklemeTarih(entity.getEklemeTarih())
                .kutu(entity.getKutu())
                .aciklama(entity.getAciklama())
                .ekleyenId(entity.getEkleyenId())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto CanakNumaralarDTO
     * @return CanakNumaralar entity
     */
    public CanakNumaralar toEntity(CanakNumaralarDTO dto) {
        if (dto == null) {
            return null;
        }

        return CanakNumaralar.builder()
                .id(dto.getId())
                .canakNo(dto.getCanakNo())
                .kurumKod(dto.getKurumKod())
                .eklemeTarih(dto.getEklemeTarih())
                .kutu(dto.getKutu())
                .aciklama(dto.getAciklama())
                .ekleyenId(dto.getEkleyenId())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity CanakNumaralar entity to update
     * @param dto CanakNumaralarDTO with new values
     * @return Updated CanakNumaralar entity
     */
    public CanakNumaralar updateEntityFromDto(CanakNumaralar entity, CanakNumaralarDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setCanakNo(dto.getCanakNo());
        entity.setKurumKod(dto.getKurumKod());
        entity.setEklemeTarih(dto.getEklemeTarih());
        entity.setKutu(dto.getKutu());
        entity.setAciklama(dto.getAciklama());
        entity.setEkleyenId(dto.getEkleyenId());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of CanakNumaralar entities
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> toDtoList(List<CanakNumaralar> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of CanakNumaralarDTO
     * @return List of CanakNumaralar entities
     */
    public List<CanakNumaralar> toEntityList(List<CanakNumaralarDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
