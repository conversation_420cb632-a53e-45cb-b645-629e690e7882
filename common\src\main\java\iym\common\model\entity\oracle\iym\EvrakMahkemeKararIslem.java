package iym.common.model.entity.oracle.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for EVRAK_MAHKEME_KARAR_ISLEM table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "EvrakMahkemeKararIslem")
@Table(name = "EVRAK_MAHKEME_KARAR_ISLEM")
public class EvrakMahkemeKararIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "EVRAK_ID", nullable = false)
    private Long evrakId;

    @Column(name = "KURUM", length = 10)
    @Size(max = 10)
    private String kurum;

    @Column(name = "SEVIYE", length = 1)
    @Size(max = 1)
    private String seviye;
}
