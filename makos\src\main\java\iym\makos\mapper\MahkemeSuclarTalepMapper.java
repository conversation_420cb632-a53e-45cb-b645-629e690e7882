package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.MahkemeSuclarTalep;
import org.springframework.stereotype.Component;
import iym.makos.dto.MahkemeSuclarTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeSuclarTalep entity and DTO
 */
@Component
public class MahkemeSuclarTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity MahkemeSuclarTalep entity
     * @return MahkemeSuclarTalepDTO
     */
    public MahkemeSuclarTalepDTO toDto(MahkemeSuclarTalep entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeSuclarTalepDTO.builder()
                .id(entity.getId())
                .mahkemeKararTalepId(entity.getMahkemeKararTalepId())
                .sucTipKodu(entity.getSucTipKodu())
                .durumu(entity.getDurumu())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto MahkemeSuclarTalepDTO
     * @return MahkemeSuclarTalep entity
     */
    public MahkemeSuclarTalep toEntity(MahkemeSuclarTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeSuclarTalep.builder()
                .id(dto.getId())
                .mahkemeKararTalepId(dto.getMahkemeKararTalepId())
                .sucTipKodu(dto.getSucTipKodu())
                .durumu(dto.getDurumu())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity MahkemeSuclarTalep entity to update
     * @param dto MahkemeSuclarTalepDTO with new values
     * @return Updated MahkemeSuclarTalep entity
     */
    public MahkemeSuclarTalep updateEntityFromDto(MahkemeSuclarTalep entity, MahkemeSuclarTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setMahkemeKararTalepId(dto.getMahkemeKararTalepId());
        entity.setSucTipKodu(dto.getSucTipKodu());
        entity.setDurumu(dto.getDurumu());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities List of MahkemeSuclarTalep entities
     * @return List of MahkemeSuclarTalepDTO
     */
    public List<MahkemeSuclarTalepDTO> toDtoList(List<MahkemeSuclarTalep> entities) {
        if (entities == null) {
            return List.of();
        }

        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
