package iym.makos.validation;

import iym.common.model.api.HedefTip;
import iym.common.model.api.HedefWithAdSoyad;
import iym.common.model.api.KararTuru;
import iym.common.model.entity.oracle.iym.Hedefler;
import iym.common.model.entity.oracle.iym.MahkemeKarar;
import iym.common.service.db.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDHedefAdSoyadGuncellemeRequest;
import iym.makos.model.api.HedefAdSoyadGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class IDHedefAdSoyadGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDHedefAdSoyadGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;

    @Autowired
    public IDHedefAdSoyadGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService,
                                             DbHedeflerService dbHedeflerService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
    }

    @Override
    public ValidationResult validate(IDHedefAdSoyadGuncellemeRequest request) {

        try {
            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            for (HedefAdSoyadGuncellemeKararDetay adSoyadGuncellemeDetay : request.getHedefAdSoyadGuncellemeKararDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = adSoyadGuncellemeDetay.getMahkemeKararDetay();
                Optional<MahkemeKarar> iliskiliMahkemeKararDb = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetay.getMahkemeKodu()
                        , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetay.getSorusturmaNo());

                if (iliskiliMahkemeKararDb.isEmpty()) {
                    String errorStr = String.format("Güncellemeye Konu Mahkeme Karar Bulunamadı: Mahkeme İl/İlçe Kodu: %s Mahkeme Kodu: %s, Karar No: %s, Soruşturma No :%s"
                            , iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());
                    validationResult.addFailedReason(errorStr);
                } else {

                    MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararDb.get();

                    List<HedefWithAdSoyad> guncellemeList = adSoyadGuncellemeDetay.getHedefAdSoyadListesi();
                    for (HedefWithAdSoyad hedefWithAdSoyad : guncellemeList) {

                        String yeniHedefAd = hedefWithAdSoyad.getHedefAd();
                        String yeniHedefSoyad = hedefWithAdSoyad.getHedefSoyad();
                        String hedefNo = hedefWithAdSoyad.getHedef().getHedefNo();
                        HedefTip hedefTip = hedefWithAdSoyad.getHedef().getHedefTip();

                        //Sistemde bu hedef var mi?
                        Optional<Hedefler> existingHedefBilgisiOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTip);

                        if (existingHedefBilgisiOpt.isEmpty()) {
                            validationResult.addFailedReason(hedefNo + " numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
                        } else {
                            Hedefler existingHedefBilgisi = existingHedefBilgisiOpt.get();
                            if (existingHedefBilgisi.getHedefAdi().equals(yeniHedefAd) && existingHedefBilgisi.getHedefSoyadi().equals(yeniHedefSoyad)) {
                                validationResult.addFailedReason(hedefNo + " numaralı hedef bilgisi zaten eskisi ile aynıdır");
                            }
                        }
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME;
    }

}

