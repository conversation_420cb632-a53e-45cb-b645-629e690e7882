package iym.backend.controller;

import iym.backend.config.security.IymUserDetailsImpl;
import iym.backend.config.security.JwtTokenUtil;
import iym.backend.model.auth.AuthResponse;
import iym.backend.model.auth.LoginRequest;
import iym.backend.model.auth.LoginResponse;
import iym.backend.model.auth.UserRequest;
import iym.common.model.entity.oracle.iym.IymUser;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.service.db.DbIymUserService;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;

/**
 * Authentication Controller for IYM Backend
 * Handles login, register and JWT token operations
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {

    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Autowired
    JwtTokenUtil tokenProvider;

    @Autowired
    private DbIymUserService userService;

    /**
     * User login endpoint
     */
    @PermitAll
    @PostMapping("/login")
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequest request) {
        try {
            log.info("Got login request for user:{}", request.getUsername());
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

            if (authentication.isAuthenticated()) {
                IymUserDetailsImpl userDetailsImpl = (IymUserDetailsImpl) authentication.getPrincipal();

                if (!userDetailsImpl.getUserRole().canLogin()) {
                    log.error("User can't login! Invalid role:{}, requester:{}", 
                            userDetailsImpl.getUserRole(), request.getUsername());
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(new AuthResponse("Login Not Allowed for User", false));
                }

                LoginResponse loginResponse = createLoginResponse(userDetailsImpl);
                log.info("User logging in {}", userDetailsImpl);
                return ResponseEntity.ok(loginResponse);
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new AuthResponse("Invalid Credentials", false));
            }
        } catch (DisabledException ex) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new AuthResponse("User is disabled", false));
        } catch (BadCredentialsException ex) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new AuthResponse("Invalid Credentials", false));
        } catch (Exception ex) {
            log.error("Login failed for user:{}", request.getUsername(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new AuthResponse("Login failed (Internal Error)", false));
        }
    }

    /**
     * User registration endpoint
     */
    @PermitAll
    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody UserRequest registerRequest) {
        if (userService.findByUsername(registerRequest.getUserName()).isPresent()) {
            return ResponseEntity.badRequest()
                    .body(new AuthResponse("User already exist", false));
        }

        IymUser user = IymUser.builder()
                .username(registerRequest.getUserName())
                .password(passwordEncoder.encode(registerRequest.getPassword()))
                .status(UserStatusType.ACTIVE)
                .role(registerRequest.getRole() != null ? registerRequest.getRole() : IymUserRoleType.ROLE_ADMIN)
                .kurum(registerRequest.getKurum())
                .build();

        userService.save(user);

        try {
            Authentication authenticate = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            registerRequest.getUserName(),
                            registerRequest.getPassword())
            );

            if (authenticate.isAuthenticated()) {
                SecurityContextHolder.getContext().setAuthentication(authenticate);
            }
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
        }

        return ResponseEntity.ok(new AuthResponse("User registered successfully", true));
    }

    /**
     * Change password endpoint
     */
    @PostMapping("/changePassword")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<?> changePassword(@Valid @RequestBody LoginRequest request, Authentication auth) {
        try {
            @SuppressWarnings("unchecked")
            Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) auth.getAuthorities();
            String secureUserRole = authorities.iterator().next().getAuthority();
            
            // TODO: Implement password change logic
            log.info("Password change requested by user: {}, role: {}", auth.getName(), secureUserRole);
            
            return ResponseEntity.ok(new AuthResponse("Password changed successfully", true));
        } catch (Exception ex) {
            log.error("Password change failed for user: {}", auth.getName(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new AuthResponse("Password change failed", false));
        }
    }

    /**
     * Create login response with JWT token
     */
    private LoginResponse createLoginResponse(IymUserDetailsImpl userDetails) {
        String jwtToken = tokenProvider.generateJwtToken(userDetails);

        return new LoginResponse(
                jwtToken,
                userDetails.getId(),
                userDetails.getUsername(),
                userDetails.getActingUserName(),
                new HashSet<>(List.of(userDetails.getUserRole().name())),
                userDetails.getKurum()
        );
    }
}
