# Multi-DataSource Test ve Doğrulama Stratejisi

## 🎯 Amaç
Multi-datasource geçişinin güvenli ve sorunsuz bir şekilde yapılması için kapsamlı test ve doğrulama stratejisi.

## 📋 Test Aşamaları

### Faz 1: <PERSON><PERSON>ler<PERSON> (Pre-Migration Tests)

#### 1.1 Mevcut Sistem Baseline Testleri
```bash
# Backend modülü testleri
cd backend
mvn clean test

# Makos modülü testleri  
cd makos
mvn clean test

# Integration testleri
mvn clean verify -P integration-test
```

#### 1.2 Database Connection Testleri
```java
@Test
public void testOracleConnectionBeforeMigration() {
    // Mevcut Oracle bağlantısını test et
    assertThat(dataSource.getConnection()).isNotNull();
}
```

### Faz 2: Migration Testleri

#### 2.1 Configuration Testleri
```java
@TestPropertySource(properties = {
    "app.datasource.mode=multi"
})
@SpringBootTest
public class MultiDataSourceConfigTest {
    
    @Autowired
    @Qualifier("oracleDataSource")
    private DataSource oracleDataSource;
    
    @Autowired
    @Qualifier("oracleEntityManagerFactory")
    private EntityManagerFactory oracleEntityManagerFactory;
    
    @Test
    public void testOracleDataSourceConfiguration() {
        assertThat(oracleDataSource).isNotNull();
        assertThat(oracleDataSource).isInstanceOf(HikariDataSource.class);
    }
    
    @Test
    public void testOracleEntityManagerFactory() {
        assertThat(oracleEntityManagerFactory).isNotNull();
        assertThat(oracleEntityManagerFactory.isOpen()).isTrue();
    }
}
```

#### 2.2 Repository Testleri
```java
@DataJpaTest
@TestPropertySource(properties = {
    "app.datasource.mode=multi"
})
public class OracleRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private IymUserRepo iymUserRepo;
    
    @Test
    public void testIymUserRepository() {
        // Repository'nin çalıştığını doğrula
        List<IymUser> users = iymUserRepo.findAll();
        assertThat(users).isNotNull();
    }
}
```

### Faz 3: Functional Testleri

#### 3.1 Backend Modülü API Testleri
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "app.datasource.mode=multi"
})
public class BackendApiTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testAuthLogin() {
        // Login endpoint'ini test et
        LoginRequest request = new LoginRequest("testuser", "password");
        ResponseEntity<LoginResponse> response = restTemplate.postForEntity(
            "/api/auth/login", request, LoginResponse.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

#### 3.2 Makos Modülü API Testleri
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "app.datasource.mode=multi"
})
public class MakosApiTest {
    
    @Test
    public void testMahkemeKararEndpoints() {
        // Mahkeme karar endpoint'lerini test et
    }
    
    @Test
    public void testUserManagementEndpoints() {
        // User management endpoint'lerini test et
    }
}
```

### Faz 4: Performance Testleri

#### 4.1 Connection Pool Testleri
```java
@Test
public void testConnectionPoolPerformance() {
    // Connection pool performansını test et
    long startTime = System.currentTimeMillis();
    
    for (int i = 0; i < 100; i++) {
        try (Connection conn = oracleDataSource.getConnection()) {
            // Basit query çalıştır
            conn.prepareStatement("SELECT 1 FROM DUAL").executeQuery();
        }
    }
    
    long endTime = System.currentTimeMillis();
    assertThat(endTime - startTime).isLessThan(5000); // 5 saniyeden az
}
```

#### 4.2 Memory Usage Testleri
```java
@Test
public void testMemoryUsage() {
    // Memory kullanımını test et
    Runtime runtime = Runtime.getRuntime();
    long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
    
    // Entity'leri yükle
    entityManager.createQuery("SELECT e FROM IymUser e").getResultList();
    
    long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
    long memoryUsed = memoryAfter - memoryBefore;
    
    // Memory kullanımının makul seviyede olduğunu doğrula
    assertThat(memoryUsed).isLessThan(50 * 1024 * 1024); // 50MB'dan az
}
```

## 🔧 Test Araçları ve Komutları

### Maven Test Komutları
```bash
# Tüm testleri çalıştır
mvn clean test

# Sadece unit testleri
mvn test -Dtest="*Test"

# Sadece integration testleri
mvn test -Dtest="*IT"

# Coverage raporu ile
mvn clean test jacoco:report

# Specific profile ile
mvn test -Ptest-multi-datasource
```

### Test Profilleri
```properties
# application-test-single.properties
app.datasource.mode=single

# application-test-multi.properties
app.datasource.mode=multi
spring.datasource.oracle.url=jdbc:h2:mem:testdb
spring.datasource.oracle.driver-class-name=org.h2.Driver
```

## 📊 Test Checklist

### ✅ Pre-Migration Checklist
- [ ] Mevcut tüm testler geçiyor
- [ ] Database bağlantısı çalışıyor
- [ ] API endpoint'leri çalışıyor
- [ ] Performance baseline alındı

### ✅ Post-Migration Checklist
- [ ] OracleJpaConfig doğru yükleniyor
- [ ] Oracle DataSource bağlantısı çalışıyor
- [ ] Entity'ler doğru package'dan yükleniyor
- [ ] Repository'ler çalışıyor
- [ ] Transaction yönetimi çalışıyor
- [ ] API endpoint'leri çalışıyor
- [ ] Performance degradation yok
- [ ] Memory kullanımı normal seviyede

## 🚨 Rollback Stratejisi

### Hızlı Rollback
```properties
# application.properties'de
app.datasource.mode=single
```

### Tam Rollback
```bash
# Backup'tan geri yükle
cp backend/src/main/resources/application.properties.backup \
   backend/src/main/resources/application.properties

# DbLoader'daki değişiklikleri geri al
git checkout -- database/src/main/java/iym/spring/db/loader/DbLoader.java
```

## 📈 Monitoring ve Alerting

### Application Startup Monitoring
```java
@EventListener
public void handleApplicationReady(ApplicationReadyEvent event) {
    log.info("Application started with datasource mode: {}", 
             environment.getProperty("app.datasource.mode"));
    
    // DataSource health check
    try {
        oracleDataSource.getConnection().close();
        log.info("Oracle DataSource connection successful");
    } catch (Exception e) {
        log.error("Oracle DataSource connection failed", e);
    }
}
```

### Health Check Endpoints
```java
@RestController
public class DataSourceHealthController {
    
    @GetMapping("/health/datasource")
    public ResponseEntity<Map<String, String>> checkDataSourceHealth() {
        Map<String, String> status = new HashMap<>();
        
        try {
            oracleDataSource.getConnection().close();
            status.put("oracle", "UP");
        } catch (Exception e) {
            status.put("oracle", "DOWN");
        }
        
        return ResponseEntity.ok(status);
    }
}
```

## 🎯 Success Criteria

1. **Functional**: Tüm API endpoint'leri çalışıyor
2. **Performance**: %10'dan fazla performance degradation yok
3. **Stability**: 24 saat boyunca hata yok
4. **Memory**: Memory leak yok
5. **Logging**: Hata logları temiz
