package iym.makos.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * Temporary controller to test database connectivity
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class DatabaseTestController {

    @Autowired
    @Qualifier("oracleDataSource")
    private DataSource dataSource;

    @GetMapping("/db-connection")
    public ResponseEntity<Map<String, Object>> testDatabaseConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("Testing database connection...");
            
            try (Connection connection = dataSource.getConnection()) {
                log.info("Database connection successful!");
                
                // Test basic query
                try (Statement stmt = connection.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL")) {
                    
                    if (rs.next()) {
                        result.put("status", "SUCCESS");
                        result.put("message", "Database connection and query successful");
                        result.put("testQuery", "SELECT 1 FROM DUAL");
                        result.put("result", rs.getInt(1));
                    }
                }
                
                // Test if MAKOS_USER table exists and has data
                try (Statement stmt = connection.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM iym.MAKOS_USER")) {
                    
                    if (rs.next()) {
                        int userCount = rs.getInt(1);
                        result.put("makosUserCount", userCount);
                        log.info("MAKOS_USER table has {} users", userCount);
                    }
                } catch (Exception e) {
                    result.put("makosUserTableError", e.getMessage());
                    log.warn("Could not query MAKOS_USER table: {}", e.getMessage());
                }
                
                // Test specific user
                try (Statement stmt = connection.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT USERNAME, STATUS, ROLE FROM iym.MAKOS_USER WHERE USERNAME = 'makos_admin'")) {
                    
                    if (rs.next()) {
                        Map<String, String> user = new HashMap<>();
                        user.put("username", rs.getString("USERNAME"));
                        user.put("status", rs.getString("STATUS"));
                        user.put("role", rs.getString("ROLE"));
                        result.put("makosAdminUser", user);
                        log.info("Found makos_admin user: {}", user);
                    } else {
                        result.put("makosAdminUser", "NOT_FOUND");
                        log.warn("makos_admin user not found in database");
                    }
                } catch (Exception e) {
                    result.put("makosAdminUserError", e.getMessage());
                    log.warn("Could not query makos_admin user: {}", e.getMessage());
                }
                
            }
            
        } catch (Exception e) {
            log.error("Database connection failed", e);
            result.put("status", "ERROR");
            result.put("message", "Database connection failed: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(result);
        }
        
        return ResponseEntity.ok(result);
    }
}
