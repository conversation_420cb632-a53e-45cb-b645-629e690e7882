package iym.db.jpa.oracle.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.oracle.iym.MahkemeKarar;
import iym.common.service.db.DbMahkemeKararService;
import iym.db.jpa.oracle.dao.MahkemeKararRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKararServiceImpl extends GenericDbServiceImpl<MahkemeKarar, Long> implements DbMahkemeKararService {

    private final MahkemeKararRepo mahkemeKararRepo;

    @Autowired
    public DbMahkemeKararServiceImpl(MahkemeKararRepo repository) {
        super(repository);
        this.mahkemeKararRepo = repository;
    }

    /*
    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKarar> findById(Long evrakId) {
        return mahkemeKararRepo.findById(evrakId);
    }

     */

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKarar> findByEvrakId(Long evrakId){
        return mahkemeKararRepo.findByEvrakId(evrakId);
    }

    public Optional<MahkemeKarar> findBy(
            String mahkemeIlIlceKodu,
            String mahkemeKodu,
            String mahkemeKararNo,
            String sorusturmaNo
    ){
        return mahkemeKararRepo.findBy(mahkemeIlIlceKodu, mahkemeKodu, mahkemeKararNo, sorusturmaNo);
    }

}
