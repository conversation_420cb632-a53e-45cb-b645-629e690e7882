package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.MahkemeBilgi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for MahkemeBilgi entity
 */
@Repository
public interface MahkemeBilgisiRepo extends JpaRepository<MahkemeBilgi, Long> {

    Optional<MahkemeBilgi> findByMahkemeKodu(String mahkemeKodu);

}
