package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for MahkemeKararTalep entity
 */
@Repository
public interface MahkemeKararTalepRepo extends JpaRepository<MahkemeKararTalep, Long> {

    List<MahkemeKararTalep> findByEvrakId(Long evrakId);
    

}
