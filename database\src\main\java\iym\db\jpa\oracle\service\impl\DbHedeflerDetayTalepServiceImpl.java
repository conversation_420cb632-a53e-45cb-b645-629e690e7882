package iym.db.jpa.oracle.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.oracle.iym.HedeflerDetayTalep;
import iym.common.service.db.DbHedeflerDetayTalepService;
import iym.db.jpa.oracle.dao.HedeflerDetayTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbHedeflerDetayTalepServiceImpl extends GenericDbServiceImpl<HedeflerDetayTalep, Long> implements DbHedeflerDetayTalepService {

    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;

    @Autowired
    public DbHedeflerDetayTalepServiceImpl(HedeflerDetayTalepRepo repository) {
        super(repository);
        this.hedeflerDetayTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId){
        return hedeflerDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }


}
