package iym.db.jpa.oracle.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.oracle.iym.DetayMahkemeKararTalep;
import iym.common.service.db.DbDetayMahkemeKararTalepService;
import iym.db.jpa.oracle.dao.DetayMahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbDetayMahkemeKararTalepServiceImpl extends GenericDbServiceImpl<DetayMahkemeKararTalep, Long> implements DbDetayMahkemeKararTalepService {

    private final DetayMahkemeKararTalepRepo  detayMahkemeKararTalepRepo;

    @Autowired
    public DbDetayMahkemeKararTalepServiceImpl(DetayMahkemeKararTalepRepo repository) {
        super(repository);
        this.detayMahkemeKararTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararTalep> findByEvrakId(Long evrakId){
        return detayMahkemeKararTalepRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId){
        return detayMahkemeKararTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }


}
