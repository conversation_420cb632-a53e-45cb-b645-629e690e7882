package iym.db.jpa.service.impl;

import iym.common.model.entity.oracle.iym.MahkemeAidiyatDetayTalep;
import iym.db.jpa.oracle.dao.MahkemeAidiyatDetayTalepRepo;
import iym.db.jpa.oracle.service.impl.DbMahkemeAidiyatDetayTalepServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DbMahkemeAidiyatDetayTalepServiceImplTest {

    @Mock
    private MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    @InjectMocks
    private DbMahkemeAidiyatDetayTalepServiceImpl dbMahkemeAidiyatDetayTalepService;

    private MahkemeAidiyatDetayTalep mahkemeAidiyatDetayTalep;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();
        mahkemeAidiyatDetayTalep = MahkemeAidiyatDetayTalep.builder()
                .id(1L)
                .iliskiliMahkemeKararId(100L)
                .mahkemeKararTalepId(200L)
                .mahkemeAidiyatKoduEkle("AIDIYAT-EKLE")
                .mahkemeAidiyatKoduCikar(null)
                .tarih(testDate)
                .durum("AKTIF")
                .mahkemeKararDetayTalepId(300L)
                .build();
    }

    @Test
    void findByMahkemeKararId_shouldReturnListOfMahkemeAidiyatDetayTalep() {
        // Given
        Long mahkemeKararId = 200L;
        List<MahkemeAidiyatDetayTalep> expectedList = Arrays.asList(mahkemeAidiyatDetayTalep);
        when(mahkemeAidiyatDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararId)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatDetayTalep> result = dbMahkemeAidiyatDetayTalepService.findByMahkemeKararId(mahkemeKararId);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatDetayTalepRepo).findByMahkemeKararTalepId(mahkemeKararId);
    }

    @Test
    void findByIliskiliMahkemeKararId_shouldReturnListOfMahkemeAidiyatDetayTalep() {
        // Given
        Long iliskiliMahkemeKararId = 100L;
        List<MahkemeAidiyatDetayTalep> expectedList = Arrays.asList(mahkemeAidiyatDetayTalep);
        when(mahkemeAidiyatDetayTalepRepo.findByIliskiliMahkemeKararId(iliskiliMahkemeKararId)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatDetayTalep> result = dbMahkemeAidiyatDetayTalepService.findByIliskiliMahkemeKararId(iliskiliMahkemeKararId);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatDetayTalepRepo).findByIliskiliMahkemeKararId(iliskiliMahkemeKararId);
    }

    @Test
    void findByMahkemeKararDetayId_shouldReturnListOfMahkemeAidiyatDetayTalep() {
        // Given
        Long mahkemeKararDetayId = 300L;
        List<MahkemeAidiyatDetayTalep> expectedList = Arrays.asList(mahkemeAidiyatDetayTalep);
        when(mahkemeAidiyatDetayTalepRepo.findByMahkemeKararDetayTalepId(mahkemeKararDetayId)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatDetayTalep> result = dbMahkemeAidiyatDetayTalepService.findByMahkemeKararDetayId(mahkemeKararDetayId);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatDetayTalepRepo).findByMahkemeKararDetayTalepId(mahkemeKararDetayId);
    }

    @Test
    void findByDurum_shouldReturnListOfMahkemeAidiyatDetayTalep() {
        // Given
        String durum = "AKTIF";
        List<MahkemeAidiyatDetayTalep> expectedList = Arrays.asList(mahkemeAidiyatDetayTalep);
        when(mahkemeAidiyatDetayTalepRepo.findByDurum(durum)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatDetayTalep> result = dbMahkemeAidiyatDetayTalepService.findByDurum(durum);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatDetayTalepRepo).findByDurum(durum);
    }

    @Test
    void findByTarihBetween_shouldReturnListOfMahkemeAidiyatDetayTalep() {
        // Given
        Date startDate = new Date(testDate.getTime() - 86400000); // One day before
        Date endDate = new Date(testDate.getTime() + 86400000); // One day after
        List<MahkemeAidiyatDetayTalep> expectedList = Arrays.asList(mahkemeAidiyatDetayTalep);
        when(mahkemeAidiyatDetayTalepRepo.findByTarihBetween(startDate, endDate)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatDetayTalep> result = dbMahkemeAidiyatDetayTalepService.findByTarihBetween(startDate, endDate);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatDetayTalepRepo).findByTarihBetween(startDate, endDate);
    }


}
