package iym.makos.validation;

import iym.common.model.api.Evrak<PERSON>urum;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.model.entity.oracle.iym.SucTipi;
import iym.common.service.db.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDYeniKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Optional;

@Service
@Slf4j
public class IDYeniKararValidator extends MahkemeKararRequestValidatorBase<IDYeniKararRequest> {

    public static final LocalDate OHAL_DATE = LocalDate.parse("2018-07-19");
    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public IDYeniKararValidator(DbMahkemeKararService dbMahkemeKararService,
                                DbHedeflerService dbHedeflerService,
                                DbSucTipiService dbSucTipiService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    public ValidationResult validate(IDYeniKararRequest request) {

        try {
            log.error("IDYeniKararValidator Validation");

            //Validate MahkemeKararRequestCommonValidator;
            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            //null kontrolu yapmaya gerek yok cunku request'te  @NotNull annotation var.
            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);
            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();

            //Ohal Kontrolu
            if (LocalDate.now().isAfter(OHAL_DATE) && kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)) {
                validationResult.addFailedReason("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
            }

            Optional.ofNullable(request.getMahkemeSucTipiKodlari())
                    .orElse(Collections.emptyList())
                    .forEach(sucTipiKodu -> {
                        Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                        if (sucTipiOpt.isEmpty()) {
                            validationResult.addFailedReason(String.format( "'%s' kodu ile eşleşen bir suç tipi mevcut değil.", sucTipiKodu));
                        }
                    });

            Optional.ofNullable(request.getMahkemeAidiyatKodlari())
                    .orElse(Collections.emptyList())
                    .forEach(aidiyatKod -> {
                        String uyariMesaj = CommonUtils.aidiyatEklemeUyarisi(aidiyatKod, kararTipi, evrakKurum);
                        if (!CommonUtils.isNullOrEmpty(uyariMesaj)) {
                            validationResult.addFailedReason(uyariMesaj);
                        }
                    });

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI;
    }

}

