package iym.makos.controller.filters;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.model.entity.oracle.makos.MakosUserAuditLog;
import iym.common.model.enums.MakosUserAuditType;
import iym.common.service.db.DbMakosUserAuditLogService;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.controller.filters.util.CachedBodyHttpServletRequest;
import iym.common.util.HttpUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Filter for auditing user operations in MAKOS module
 * Logs all user-related requests and responses for security and compliance
 */
@Slf4j
public class MakosUserAuditLogFilter implements Filter {

    private final DbMakosUserAuditLogService makosUserAuditLogService;

    public MakosUserAuditLogFilter(DbMakosUserAuditLogService makosUserAuditLogService) {
        this.makosUserAuditLogService = makosUserAuditLogService;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        log.debug("MAKOS User Audit Log Filter started for request");

        //----------  LOG REQUEST  ----------
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetailsImpl = null;

        if (authentication != null && !authentication.getPrincipal().equals("anonymousUser")) {
            userDetailsImpl = (UserDetailsImpl) authentication.getPrincipal();
        }

        CachedBodyHttpServletRequest cachedRequest = new CachedBodyHttpServletRequest((HttpServletRequest) servletRequest);
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        // request body json map - used to obtain log params from request body
        byte[] inputStreamBytes = StreamUtils.copyToByteArray(cachedRequest.getInputStream());

        // requested api function
        URI uri = null;
        try {
            uri = new URI(cachedRequest.getRequestURL().toString());
        } catch (URISyntaxException e) {
            log.error("Can't get uri from {}", cachedRequest.getRequestURL().toString(), e);
            throw new RuntimeException(e);
        }
        String[] segments = uri.getPath().split("/");
        MakosUserAuditType userAuditType = MakosUserAuditType.getUserAuditType(segments[segments.length - 1]);

        // client ip address info
        String ipAddress = HttpUtils.getClientIpAddress((HttpServletRequest) servletRequest);

        // build user audit log with request information
        MakosUserAuditLog userAuditLog = new MakosUserAuditLog();
        userAuditLog.setUserAuditType(userAuditType);
        // login requests does not have authentication
        userAuditLog.setUsername(userDetailsImpl == null ? "anonymousUser" : userDetailsImpl.getUsername());
        userAuditLog.setActingUsername(userDetailsImpl == null ? null : userDetailsImpl.getActingUserName());
        userAuditLog.setUserIp(ipAddress);

        // Extract admin operated username from request body for specific operations
        if (userAuditType == MakosUserAuditType.ACTIVATE || userAuditType == MakosUserAuditType.DEACTIVATE
                || userAuditType == MakosUserAuditType.LOGIN) {
            try {
                if (inputStreamBytes.length > 0) {
                    Map<String, Object> jsonRequest = new ObjectMapper().readValue(inputStreamBytes, Map.class);
                    userAuditLog.setAdminOperatedUsername((String) jsonRequest.get("username"));
                }
            } catch (Exception e) {
                log.warn("Could not parse request body for audit logging", e);
            }
        } else if (userAuditType == MakosUserAuditType.REGISTER) {
            try {
                if (inputStreamBytes.length > 0) {
                    Map<String, Object> jsonRequest = new ObjectMapper().readValue(inputStreamBytes, Map.class);
                    userAuditLog.setAdminOperatedUsername((String) jsonRequest.get("userName"));
                }
            } catch (Exception e) {
                log.warn("Could not parse request body for audit logging", e);
            }
        } else if (userAuditType == MakosUserAuditType.ADD || userAuditType == MakosUserAuditType.UPDATE) {
            try {
                if (inputStreamBytes.length > 0) {
                    Map<String, Object> jsonRequest = new ObjectMapper().readValue(inputStreamBytes, Map.class);
                    Map<String, String> userJsonMap = (Map<String, String>) jsonRequest.get("user");
                    if (userJsonMap != null) {
                        userAuditLog.setAdminOperatedUsername(userJsonMap.get("username"));
                    }
                }
            } catch (Exception e) {
                log.warn("Could not parse request body for audit logging", e);
            }
        }

        userAuditLog.setRequestTime(LocalDateTime.now());

        // save to db, then use the same object to write response info after filter chain
        makosUserAuditLogService.save(userAuditLog);
        log.debug("MAKOS User Audit Log Filter ended for request");

        //----------  DO FILTER CHAIN  ----------
        filterChain.doFilter(cachedRequest, response);

        //----------  LOG RESPONSE  ----------
        log.debug("MAKOS User Audit Log Filter started for response.");
        userAuditLog.setResponseTime(LocalDateTime.now());
        userAuditLog.setResponseCode(response.getStatus());
        makosUserAuditLogService.save(userAuditLog);
        log.debug("MAKOS User Audit Log Filter ended for response");
    }
}
