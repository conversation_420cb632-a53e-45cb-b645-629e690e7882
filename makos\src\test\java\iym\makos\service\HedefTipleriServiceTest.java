package iym.makos.service;

import iym.common.model.entity.oracle.iym.HedefTipleri;
import iym.common.service.db.DbHedefTipleriService;
import iym.makos.mapper.HedefTipleriMapper;
import iym.makos.service.makos.HedefTipleriService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.dto.HedefTipleriDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HedefTipleriServiceTest {

    @Mock
    private DbHedefTipleriService dbHedefTipleriService;

    @Mock
    private HedefTipleriMapper hedefTipleriMapper;

    @InjectMocks
    private HedefTipleriService hedefTipleriService;

    private HedefTipleri hedefTipleri;
    private HedefTipleriDTO hedefTipleriDTO;
    private List<HedefTipleri> hedefTipleriList;
    private List<HedefTipleriDTO> hedefTipleriDTOList;

    @BeforeEach
    void setUp() {
        hedefTipleri = HedefTipleri.builder()
                .id(1L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON")
                .sonlandirmami("E")
                .karsiligi(1L)
                .sno(1L)
                .hedefTanim("TEL")
                .durum("AKTIF")
                .hitapTip("TEL")
                .hitapIcerikTip("NUM")
                .hitapIcindemi("E")
                .hitapEh("E")
                .minl(10L)
                .maxl(10L)
                .imhaYapilsinmi("E")
                .tasinabilirmi("E")
                .aktifmi(1L)
                .hitapaGonderilecekmi(0L)
                .build();

        HedefTipleri hedefTipleri2 = HedefTipleri.builder()
                .id(2L)
                .hedefKodu(102L)
                .hedefTipi("EMAIL")
                .sonlandirmami("E")
                .karsiligi(2L)
                .sno(2L)
                .hedefTanim("EMAIL")
                .durum("AKTIF")
                .hitapTip("EMAIL")
                .hitapIcerikTip("TEXT")
                .hitapIcindemi("E")
                .hitapEh("E")
                .minl(5L)
                .maxl(50L)
                .imhaYapilsinmi("E")
                .tasinabilirmi("E")
                .aktifmi(1L)
                .hitapaGonderilecekmi(0L)
                .build();

        hedefTipleriDTO = HedefTipleriDTO.builder()
                .id(1L)
                .hedefKodu(101L)
                .hedefTipi("TELEFON")
                .sonlandirmami("E")
                .karsiligi(1L)
                .sno(1L)
                .hedefTanim("TEL")
                .durum("AKTIF")
                .hitapTip("TEL")
                .hitapIcerikTip("NUM")
                .hitapIcindemi("E")
                .hitapEh("E")
                .minl(10L)
                .maxl(10L)
                .imhaYapilsinmi("E")
                .tasinabilirmi("E")
                .aktifmi(1L)
                .hitapaGonderilecekmi(0L)
                .build();

        HedefTipleriDTO hedefTipleriDTO2 = HedefTipleriDTO.builder()
                .id(2L)
                .hedefKodu(102L)
                .hedefTipi("EMAIL")
                .sonlandirmami("E")
                .karsiligi(2L)
                .sno(2L)
                .hedefTanim("EMAIL")
                .durum("AKTIF")
                .hitapTip("EMAIL")
                .hitapIcerikTip("TEXT")
                .hitapIcindemi("E")
                .hitapEh("E")
                .minl(5L)
                .maxl(50L)
                .imhaYapilsinmi("E")
                .tasinabilirmi("E")
                .aktifmi(1L)
                .hitapaGonderilecekmi(0L)
                .build();

        hedefTipleriList = Arrays.asList(hedefTipleri, hedefTipleri2);
        hedefTipleriDTOList = Arrays.asList(hedefTipleriDTO, hedefTipleriDTO2);
    }

    @Test
    void findAll_shouldReturnAllHedefTipleri() {
        // Given
        when(dbHedefTipleriService.findAll()).thenReturn(hedefTipleriList);
        when(hedefTipleriMapper.toDtoList(hedefTipleriList)).thenReturn(hedefTipleriDTOList);

        // When
        List<HedefTipleriDTO> result = hedefTipleriService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(hedefTipleriDTOList);
        verify(dbHedefTipleriService).findAll();
        verify(hedefTipleriMapper).toDtoList(hedefTipleriList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfHedefTipleri() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<HedefTipleri> hedefTipleriPage = new PageImpl<>(hedefTipleriList, pageable, hedefTipleriList.size());
        
        when(dbHedefTipleriService.findAll(pageable)).thenReturn(hedefTipleriPage);
        when(hedefTipleriMapper.toDtoList(hedefTipleriList)).thenReturn(hedefTipleriDTOList);

        // When
        Page<HedefTipleriDTO> result = hedefTipleriService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(hedefTipleriDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbHedefTipleriService).findAll(pageable);
        verify(hedefTipleriMapper).toDtoList(hedefTipleriList);
    }

    @Test
    void findByHedefKodu_shouldReturnHedefTipleri_whenExists() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.of(hedefTipleri));
        when(hedefTipleriMapper.toDto(hedefTipleri)).thenReturn(hedefTipleriDTO);

        // When
        HedefTipleriDTO result = hedefTipleriService.findByHedefKodu(101L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedefTipleriDTO);
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(hedefTipleriMapper).toDto(hedefTipleri);
    }

    @Test
    void findByHedefKodu_shouldThrowException_whenNotExists() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedefTipleriService.findByHedefKodu(101L));
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(hedefTipleriMapper, never()).toDto(any());
    }

    @Test
    void findByHedefTipi_shouldReturnHedefTipleriList() {
        // Given
        when(dbHedefTipleriService.findByHedefTipi("TELEFON")).thenReturn(Arrays.asList(hedefTipleri));
        when(hedefTipleriMapper.toDtoList(Arrays.asList(hedefTipleri))).thenReturn(Arrays.asList(hedefTipleriDTO));

        // When
        List<HedefTipleriDTO> result = hedefTipleriService.findByHedefTipi("TELEFON");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(hedefTipleriDTO);
        verify(dbHedefTipleriService).findByHedefTipi("TELEFON");
        verify(hedefTipleriMapper).toDtoList(Arrays.asList(hedefTipleri));
    }

    @Test
    void create_shouldCreateHedefTipleri() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.empty());
        when(hedefTipleriMapper.toEntity(hedefTipleriDTO)).thenReturn(hedefTipleri);
        when(hedefTipleriMapper.toDto(hedefTipleri)).thenReturn(hedefTipleriDTO);

        // When
        HedefTipleriDTO result = hedefTipleriService.create(hedefTipleriDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedefTipleriDTO);
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(hedefTipleriMapper).toEntity(hedefTipleriDTO);
        verify(dbHedefTipleriService).save(hedefTipleri);
        verify(hedefTipleriMapper).toDto(hedefTipleri);
    }

    @Test
    void create_shouldThrowException_whenHedefTipleriAlreadyExists() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.of(hedefTipleri));

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedefTipleriService.create(hedefTipleriDTO));
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(hedefTipleriMapper, never()).toEntity(any());
        verify(dbHedefTipleriService, never()).save(any());
        verify(hedefTipleriMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateHedefTipleri() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.of(hedefTipleri));
        when(hedefTipleriMapper.updateEntityFromDto(hedefTipleri, hedefTipleriDTO)).thenReturn(hedefTipleri);
        when(hedefTipleriMapper.toDto(hedefTipleri)).thenReturn(hedefTipleriDTO);

        // When
        HedefTipleriDTO result = hedefTipleriService.update(101L, hedefTipleriDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedefTipleriDTO);
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(hedefTipleriMapper).updateEntityFromDto(hedefTipleri, hedefTipleriDTO);
        verify(dbHedefTipleriService).update(hedefTipleri);
        verify(hedefTipleriMapper).toDto(hedefTipleri);
    }

    @Test
    void update_shouldThrowException_whenHedefTipleriNotExists() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedefTipleriService.update(101L, hedefTipleriDTO));
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(hedefTipleriMapper, never()).updateEntityFromDto(any(), any());
        verify(dbHedefTipleriService, never()).update(any());
        verify(hedefTipleriMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteHedefTipleri() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.of(hedefTipleri));

        // When
        hedefTipleriService.delete(101L);

        // Then
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(dbHedefTipleriService).delete(hedefTipleri);
    }

    @Test
    void delete_shouldThrowException_whenHedefTipleriNotExists() {
        // Given
        when(dbHedefTipleriService.findByHedefKodu(101L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedefTipleriService.delete(101L));
        verify(dbHedefTipleriService).findByHedefKodu(101L);
        verify(dbHedefTipleriService, never()).delete(any());
    }
}
