package iym.common.model.entity.oracle.makos.log;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity for MAKOS court decision request logging
 * Tracks all court decision-related requests and responses in the MAKOS module
 */
@Entity(name = "MakosKararRequestLog")
@Table(name = "MAKOS_KARAR_REQUEST_LOG")
@Data
public class MakosKararRequestLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "REQUEST_ID", nullable = false)
    @NotNull
    private UUID requestId;

    @Column(name = "REQUEST_URL", length = 500)
    private String requestURL;

    @Column(name = "REQUEST_TIME", nullable = false)
    private LocalDateTime requestTime;

    @Column(name = "USERNAME", length = 100)
    private String username;

    @Column(name = "ACTING_USERNAME", length = 100)
    private String actingUsername;

    @Column(name = "USER_IP", length = 100)
    private String userIp;

    @Column(name = "TARGET_ID", length = 100)
    private String targetId;

    @Column(name = "MAHKEME_KARAR_NO", length = 20)
    private String mahkemeKararNo;

    @Column(name = "SORUSTURMA_NO", length = 20)
    private String sorusturmaNo;

    @Column(name = "EVRAK_NO", length = 50)
    private String evrakNo;

    @Lob
    @Column(name = "REQUEST_BODY")
    @JsonIgnore
    private String requestBody;

    @Column(name = "RESPONSE_CODE")
    private Integer responseCode;

    @Column(name = "RESPONSE_TIME")
    private LocalDateTime responseTime;

    @Lob
    @Column(name = "RESPONSE_BODY")
    @JsonIgnore
    private String responseBody;

    @PrePersist
    protected void onCreate() {
        if (requestTime == null) {
            requestTime = LocalDateTime.now();
        }
    }
}
