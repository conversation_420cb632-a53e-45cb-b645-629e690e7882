package iym.makos.handler;

import iym.common.model.entity.oracle.iym.DetayMahkemeKararTalep;
import iym.common.model.entity.oracle.iym.MahkemeKarar;
import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import iym.common.model.entity.oracle.iym.MahkemeKoduDetayTalep;
import iym.db.jpa.oracle.dao.*;
import iym.makos.dto.id.IDMahkemeKoduGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKoduGuncellemeDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

@Component
@Slf4j
public class IDMahkemeKoduGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDMahkemeKoduGuncellemeRequest> {

    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private MahkemeKoduDetayTalepRepo mahkemeKoduDetayTalepRepo;

    @Autowired
    public IDMahkemeKoduGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeKoduDetayTalepRepo mahkemeKoduDetayTalepRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.mahkemeKoduDetayTalepRepo = mahkemeKoduDetayTalepRepo;
    }

    @Override
    @Transactional
    public Long handleDbSave(IDMahkemeKoduGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            for (MahkemeKoduGuncellemeDetay mahkemeKoduGuncellemeDetay : request.getMahkemeKoduGuncellemeDetayListesi()) {

                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = mahkemeKoduGuncellemeDetay.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                } else {
                    MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();

                    DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                    DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);
                    String yeniKod = mahkemeKoduGuncellemeDetay.getYeniMahkemeKodu();

                    MahkemeKoduDetayTalep mahkemeKoduDetayTalep = new MahkemeKoduDetayTalep();
                    mahkemeKoduDetayTalep.setMahkemeKararDetayId(dMahkemeKararTalep.getId());
                    mahkemeKoduDetayTalep.setMahkemeKodu(yeniKod);
                    MahkemeKoduDetayTalep savedMahkemeKoduDetayTalep = mahkemeKoduDetayTalepRepo.save(mahkemeKoduDetayTalep);
                }
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDMahkemeKoduGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

