package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.EvrakFiles;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface EvrakFilesRepo extends JpaRepository<EvrakFiles, Long> {

    List<EvrakFiles> findByEvrakId(Long evrakId);

}
