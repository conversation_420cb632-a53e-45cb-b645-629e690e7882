package iym.common.service.db;

import iym.common.model.api.HedefTip;
import iym.common.model.entity.oracle.iym.Hedefler;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for HedeflerTalep entity
 */
public interface DbHedeflerService extends GenericDbService<Hedefler, Long> {

    Optional<Hedefler> findById(Long id);

    Optional<Hedefler> findByMahkemeKararIdAndHedefNoAndHedefTipi(Long mahkemeKararId, String hedefNo, HedefTip hedefTipi);

    List<Hedefler> findByMahkemeKararId(Long mahkemeKararId);

}
