package iym.makos.service.makos;

import iym.common.model.api.*;
import iym.common.model.entity.oracle.iym.*;
import iym.common.model.enums.ResponseCode;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.db.jpa.oracle.dao.*;
import iym.makos.dto.MahkemeKararTalepDTO;
import iym.makos.dto.id.*;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.handler.MahkemeKararRequestDBSaveHandlerFactory;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.mapper.MahkemeKararTalepMapper;
import iym.makos.model.api.*;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.utils.UtilService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeKararTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeKararTalepService {
    private  final UtilService utilService;

    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbEvrakKayitService dbEvrakKayitService;
    private final MahkemeKararTalepMapper mahkemeKararTalepMapper;

    private final KararRequestMapper kararRequestMapper;

    private final EvrakKayitRepo evrakKayitReporo;
    private final MahkemeBilgisiRepo mahkemeBilgisiRepo;
    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;
    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;
    private final HedeflerTalepRepo hedeflerTalepRepo;
    private final HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    //
    private final MahkemeKararRepo mahkemeKararRepo;
    private final HedeflerRepo hedeflerRepo;
    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private final MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    private final MahkemeKararRequestDBSaveHandlerFactory mahkemeKararRequestDBSaveHandlerFactory;

    /**
     * Get all mahkeme karar talep records
     * @return List of MahkemeKararTalepDTO
     */


    private ApiResponse doMahkemeKararRequest2(MahkemeKararRequest kararRequest, Long kullaniciId, String fileName) throws Exception{
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ResponseCode.SUCCESS)
                .build();
            /*
            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = 1L;

            String evrakGelenKurumKodu = kararRequest.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

            EvrakKayit savedEvrak = saveEvrakBilgileri(kararRequest.getEvrakDetay(), evrakTipi, kayitTarihi, kaydedenKullaniciId);
            if (savedEvrak == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_KAYDETMEHATASI);
            }
            Long savedEvrakId = savedEvrak.getId();

            //Evrak File Kaydet
            EvrakFiles evrakFile = idKararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

            //Save Mahkeme Karar Talep
            String mahkemeKodu = kararRequest.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgi.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
            }

            MahkemeKararTalep mahkemeKararTalep = idKararRequestMapper.toMahkemeKararTalep(kararRequest.getMahkemeKararBilgisi(),
                    savedEvrakId, kullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            if (savedMahkemeKararTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            }
            Long mahkemeKararTalepId = savedMahkemeKararTalep.getId();
            // Doğru handler çağrılır
            MahkemeKararDBSaveHandler handler = mahkemeKararRequestDBSaveHandlerFactory.getHandler(kararRequest.getClass());
            if (handler != null) {
                handler.handleDbSave(kararRequest, kayitTarihi, kullaniciId, savedEvrakId, mahkemeKararTalepId);
            } else {
                throw new IllegalArgumentException("Desteklenmeyen request tipi: " + kararRequest.getClass());
            }

        */

        return apiResponse;

    }

    @Transactional
    public ApiResponse saveMahkemeKararRequest(MahkemeKararRequest kararRequest, Long kullaniciId, String fileName) throws Exception {

        ApiResponse apiResponse = doMahkemeKararRequest2(kararRequest, kullaniciId, fileName);

        if (apiResponse.getResponseCode() == ResponseCode.FAILED){
            throw new RuntimeException(apiResponse.getResponseMessage());
        }

        return apiResponse;
    }


    private ApiResponse doSaveIletisiminDenetlenmesiTalebi(IDYeniKararRequest kararRequest, Long kullaniciID, String fileName) throws Exception{
       // boolean result = true;

        try {

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = 1L;

            String evrakGelenKurumKodu = kararRequest.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

            EvrakKayit savedEvrak = saveEvrakBilgileri(kararRequest.getEvrakDetay(), evrakTipi, kayitTarihi, kaydedenKullaniciId);
            Long savedEvrakId = savedEvrak.getId();

            //Evrak File Kaydet
            EvrakFiles evrakFile = kararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

            //Save Mahkeme Karar Talep
            String mahkemeKodu = kararRequest.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgi.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
            }

            MahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toMahkemeKararTalep(kararRequest.getMahkemeKararBilgisi(),
                    savedEvrakId, kullaniciID, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            Long mahkemeKararTalepId = savedMahkemeKararTalep.getId();

            //Mahkeme Karar Talep Aidiyatlari Kaydet
            //TODO null control???
            List<MahkemeAidiyatTalep> savedMahkemeAidiyatList = saveMahkemeKararTalepAidiyatListesi(kararRequest.getMahkemeAidiyatKodlari(),  mahkemeKararTalepId);

            //Mahkeme Karar Talep Suc Tiplerini Kaydet
            //TODO null control???
            List<MahkemeSuclarTalep> savedMahkemeSucList = saveMahkemeKararTalepSucKoduListesi(kararRequest.getMahkemeSucTipiKodlari(),  mahkemeKararTalepId);

            // TODO parametre sadece yeni karar. digerleri icin method gerekir mi?
            //Hedef Islemleri
            if (kararRequest.getKararTuru() == KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR) {
                for (HedefDetayID hedefDetayID : kararRequest.getHedefDetayListesi()) {
                    HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(hedefDetayID, mahkemeKararTalepId, kullaniciID, kayitTarihi);
                    HedeflerTalep savedHedeflerTalep = hedeflerTalepRepo.save(hedeflerTalepEntity);
                    //Hedef Aidiyat Talepleri Kaydet
                    if (hedefDetayID.getHedefAidiyatKodlari() != null) {
                        List<HedeflerAidiyatTalep> savedHedefAidiyatList = saveHedeflerTalepSucKoduListesi(hedefDetayID.getHedefAidiyatKodlari(), savedHedeflerTalep.getId(),
                                savedHedeflerTalep.getHedefNo(), kayitTarihi, kullaniciID);
                    }
                }
            } else if (kararRequest.getKararTuru() == KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI) {

                for (HedefDetayID hedefDetayID : kararRequest.getHedefDetayListesi()) {
                    String hedefNo = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefNo();
                    HedefTip hedefTipi = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefTip();

                    //Uzatmaya konu mahkeme karari bul
                    MahkemeKararDetay ilgiliMahhemeKararDetay = hedefDetayID.getIlgiliMahkemeKararDetayi();
                    Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    if (mahkemeKararOpt.isEmpty()) {
                        throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                                , ilgiliMahhemeKararDetay.getMahkemeKodu()
                                , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                                , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    }
                    MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();

                    //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
                    Optional<Hedefler> iliskiliHedef = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                    if (iliskiliHedef.isEmpty()) {
                        throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                    }

                    DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, savedEvrakId);
                    DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                    HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(hedefDetayID, mahkemeKararTalepId, kullaniciID, kayitTarihi);
                    HedeflerTalep savedHedeflerTalep = hedeflerTalepRepo.save(hedeflerTalepEntity);
                    //Hedef Aidiyat Talepleri Kaydet
                    if (hedefDetayID.getHedefAidiyatKodlari() != null) {
                        List<HedeflerAidiyatTalep> savedHedefAidiyatList = saveHedeflerTalepSucKoduListesi(hedefDetayID.getHedefAidiyatKodlari(), savedHedeflerTalep.getId(),
                                savedHedeflerTalep.getHedefNo(), kayitTarihi, kullaniciID);
                    }
                }

            } else if (kararRequest.getKararTuru() == KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI) {

                for (HedefDetayID hedefDetayID : kararRequest.getHedefDetayListesi()) {
                    String hedefNo = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefNo();
                    HedefTip hedefTipi = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefTip();

                    //Uzatmaya konu mahkeme karari bul
                    MahkemeKararDetay ilgiliMahhemeKararDetay = hedefDetayID.getIlgiliMahkemeKararDetayi();
                    Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    if (mahkemeKararOpt.isEmpty()) {
                        throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                                , ilgiliMahhemeKararDetay.getMahkemeKodu()
                                , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                                , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    }
                    MahkemeKarar mahkemeKarar = mahkemeKararOpt.get();

                    //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
                    Optional<Hedefler> iliskiliHedef = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(mahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                    if (iliskiliHedef.isEmpty()) {
                        throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                    }

                    HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(hedefDetayID, mahkemeKararTalepId, kullaniciID, kayitTarihi);
                    HedeflerTalep savedHedeflerTalep = hedeflerTalepRepo.save(hedeflerTalepEntity);
                    //Hedef Aidiyat Talepleri Kaydet
                    if (hedefDetayID.getHedefAidiyatKodlari() != null) {
                        List<HedeflerAidiyatTalep> savedHedefAidiyatList = saveHedeflerTalepSucKoduListesi(hedefDetayID.getHedefAidiyatKodlari(), savedHedeflerTalep.getId(),
                                savedHedeflerTalep.getHedefNo(), kayitTarihi, kullaniciID);
                    }
                }
            } else {
                throw new MakosResponseException(MakosResponseErrorCodes.BILINMEYEN_MAHKEME_KARAR_TIPI, kararRequest.getKararTuru().name());
            }

        }catch (Exception ex){
            log.error("Bilinmeyen bir hata oluştu", ex);
        }
        return ApiResponse.builder().build();
    }

    @Transactional
    public boolean saveHedefAdSoyadGuncellemeRequest(IDHedefAdSoyadGuncellemeRequest request, Long kullaniciId, String fileName){
        boolean result = true;
        try {

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = 1L;

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

            EvrakKayit savedEvrak = saveEvrakBilgileri(request.getEvrakDetay(), evrakTipi, kayitTarihi, kaydedenKullaniciId);
            Long savedEvrakId = savedEvrak.getId();

            //Evrak File Kaydet
            EvrakFiles evrakFile = kararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

            //Save Mahkeme Karar Talep
            String mahkemeKodu = request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgi.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
            }

            MahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toMahkemeKararTalep(request.getMahkemeKararBilgisi(),
                    savedEvrakId, kaydedenKullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            Long mahkemeKararTalepId = savedMahkemeKararTalep.getId();

            List<HedefAdSoyadGuncellemeKararDetay> guncellemeListesi = request.getHedefAdSoyadGuncellemeKararDetayListesi();

            for (HedefAdSoyadGuncellemeKararDetay hedefBilgisi : guncellemeListesi) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = hedefBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, savedEvrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                //Her bir hedefin iliskili mahkeme kararda olup olmadigini kontrol et
                List<HedefWithAdSoyad> hedefListesi = hedefBilgisi.getHedefAdSoyadListesi();
                if (hedefListesi != null && !hedefListesi.isEmpty()) {
                    for (HedefWithAdSoyad hedefWithAdSoyad : hedefListesi) {

                        String hedefNo = hedefWithAdSoyad.getHedef().getHedefNo();
                        HedefTip hedefTipi = hedefWithAdSoyad.getHedef().getHedefTip();

                        Optional<Hedefler> iliskiliHedef = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        if (iliskiliHedef.isEmpty()) {
                            throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        }

                        HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper.toHedeflerDetayTalep(hedefWithAdSoyad, iliskiliMahkemeKarar.getId(), iliskiliHedef.get().getId());

                        HedeflerDetayTalep savedHedeflerDetayTalep = hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                    }
                }

            }

        }catch (Exception ex){
            log.error("Bilinmeyen bir hata oluştu ", ex);
        }
        return result;
    }

    @Transactional
    public boolean saveAidiyatBilgisiGuncellemeRequest(IDAidiyatBilgisiGuncellemeRequest request, Long kullaniciId, String fileName){

        boolean result = true;
        try {

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = 1L;

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

            EvrakKayit savedEvrak = saveEvrakBilgileri(request.getEvrakDetay(), evrakTipi, kayitTarihi, kaydedenKullaniciId);
            Long savedEvrakId = savedEvrak.getId();

            //Evrak File Kaydet
            EvrakFiles evrakFile = kararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

            //Save Mahkeme Karar Talep
            String mahkemeKodu = request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgi.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
            }

            MahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toMahkemeKararTalep(request.getMahkemeKararBilgisi(),
                    savedEvrakId, kaydedenKullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            Long mahkemeKararTalepId = savedMahkemeKararTalep.getId();

            List<AidiyatGuncellemeKararDetay> guncellemeListesi = request.getAidiyatGuncellemeKararDetayListesi();
            for (AidiyatGuncellemeKararDetay guncellemeBilgisi : guncellemeListesi) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = guncellemeBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (!mahkemeKararOpt.isPresent()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, savedEvrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                List<AidiyatGuncellemeDetay> aidiyatGuncellemeListesi = guncellemeBilgisi.getAidiyatGuncellemeDetayList();
                if (aidiyatGuncellemeListesi != null && !aidiyatGuncellemeListesi.isEmpty()) {
                    for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : aidiyatGuncellemeListesi) {

                        GuncellemeTip guncellemeTip = aidiyatGuncellemeDetay.getGuncellemeTip();
                        String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                        MahkemeAidiyatDetayTalep talep = new MahkemeAidiyatDetayTalep();
                        talep.setMahkemeKararTalepId(mahkemeKararTalepId);
                        talep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                        talep.setTarih(kayitTarihi);
                        if(guncellemeTip == GuncellemeTip.EKLE){
                            talep.setMahkemeAidiyatKoduEkle(aidiyatGuncellemeDetay.getAidiyatKodu());
                        }
                        else{
                            talep.setMahkemeAidiyatKoduCikar(aidiyatGuncellemeDetay.getAidiyatKodu());
                        }
                        MahkemeAidiyatDetayTalep savedMahkemeAidiyatDetayTalep = mahkemeAidiyatDetayTalepRepo.save(talep);
                    }
                }
            }

        }catch (Exception ex){
            log.error("Bilinmeyen bir hata oluştu", ex);
        }

        return result;
    }

    public boolean saveMahkemeKoduGuncellemeRequest(IDMahkemeKoduGuncellemeRequest request, Long kullaniciId, String fileName){
        boolean result = true;
        try{

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = 1L;

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

            EvrakKayit savedEvrak = saveEvrakBilgileri(request.getEvrakDetay(), evrakTipi, kayitTarihi, kaydedenKullaniciId);
            Long savedEvrakId = savedEvrak.getId();

            //Evrak File Kaydet
            EvrakFiles evrakFile = kararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

            //Save Mahkeme Karar Talep
            String mahkemeKodu = request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgi.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
            }


            MahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toMahkemeKararTalep(request.getMahkemeKararBilgisi(),
                    savedEvrakId, kaydedenKullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            Long mahkemeKararTalepId = savedMahkemeKararTalep.getId();


            List<MahkemeKoduGuncellemeDetay> guncellemeListesi = request.getMahkemeKoduGuncellemeDetayListesi();
            for(MahkemeKoduGuncellemeDetay mahkemeKoduGuncellemeDetay : guncellemeListesi){

                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = mahkemeKoduGuncellemeDetay.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARAR_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, savedEvrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                String yeniKod = mahkemeKoduGuncellemeDetay.getYeniMahkemeKodu();
                //TODO: yeni tabloya ekle

            }


        }catch (Exception ex){
            log.error("Bilinmeyen bir hata oluştu", ex);
        }
        return result;
    }

    public boolean saveCanakGuncellemeRequest(IDCanakGuncellemeRequest request, Long kullaniciId, String fileName){
        boolean result = true;
        try{

        }catch (Exception ex){
            log.error("Bilinmeyen bir hata oluştu", ex);
        }
        return result;
    }

    private EvrakKayit saveEvrakBilgileri(EvrakDetay evrakDetay, String evrakTipi, Date islemTarihi, Long kaydedenKullaniciId) throws  Exception{

        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        //MahkemeKararTip kararTipi = kararRequest.getMahkemeKararDetayi().getMahkemeKararBilgisi().getMahkemeKararTipi();
        //String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        //Evrak sira numarasi
        String evrakSiraNo = utilService.getEvrakSiraNumarasi(evrakDetay.getEvrakKurumKodu(), evrakDetay.getEvrakTuru().name());
        if(CommonUtils.isNullOrEmpty(evrakSiraNo)){
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_KAYDETMEHATASI);
        }

        String gelenEvrakNo = evrakDetay.getEvrakNo();
        String evrakIlIlceKodu = evrakDetay.getGeldigiIlIlceKodu();
        String evrakKurumKodu = evrakDetay.getEvrakKurumKodu();

        //Bunu genel validatore koy
        Optional<EvrakKayit> ayniEvrak = evrakKayitReporo.findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(
                gelenEvrakNo,
                evrakIlIlceKodu,
                evrakKurumKodu);
        if(ayniEvrak.isPresent()){
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_ZATENVAR, gelenEvrakNo, evrakIlIlceKodu, evrakKurumKodu);
        }

        //Save Evrak
        EvrakKayit evrakKayit = kararRequestMapper.toEvrakKayit(evrakDetay, evrakSiraNo, evrakTipi, islemTarihi, kaydedenKullaniciId);

        return evrakKayitReporo.save(evrakKayit);
    }

    private List<MahkemeAidiyatTalep> saveMahkemeKararTalepAidiyatListesi(List<String>  aidiyatListesi, Long mahkemeKararTalepId) throws  Exception{
        List<MahkemeAidiyatTalep> result = new ArrayList<>();
        if(aidiyatListesi == null)  {
            return result;
        }
        for (String aidiyatKodu : aidiyatListesi){//kararRequest.getMahkemeKararDetayi().getAidiyatKodlari()) {
            MahkemeAidiyatTalep mahkemeAidiyatTalep = kararRequestMapper.toMahkemeAidiyatTalep(mahkemeKararTalepId, aidiyatKodu, null);
            MahkemeAidiyatTalep savedMahkemeAidiyatTalep = mahkemeAidiyatTalepRepo.save(mahkemeAidiyatTalep);
            result.add(savedMahkemeAidiyatTalep);
        }

        return result;
    }

    private List<MahkemeSuclarTalep> saveMahkemeKararTalepSucKoduListesi(List<String>  sucKoduListesi, Long mahkemeKararTalepId) throws  Exception{
        List<MahkemeSuclarTalep> result = new ArrayList<>();

        if(sucKoduListesi == null){
            return result;
        }

        for (String sucTipiKodu : sucKoduListesi) {
            MahkemeSuclarTalep mahkemeSuclarTalep = kararRequestMapper.toMahkemeSuclarTalep(mahkemeKararTalepId, sucTipiKodu, null);
            MahkemeSuclarTalep savedMahkemeSuclarTalep = mahkemeSuclarTalepRepo.save(mahkemeSuclarTalep);
            result.add(savedMahkemeSuclarTalep);
        }
        return result;
    }


    private List<HedeflerAidiyatTalep> saveHedeflerTalepSucKoduListesi(List<String>  sucKoduListesi, Long hedeflerTalepId,String hedefNo, Date islemTarihi, Long kullaniciId) throws  Exception{
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : sucKoduListesi) {
            HedeflerAidiyatTalep hedeflerAidiyatTalep = kararRequestMapper.toHedeflerAidiyatTalep(kullaniciId, hedeflerTalepId, aidiyatKodu, islemTarihi);
            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = hedeflerAidiyatTalepRepo.save(hedeflerAidiyatTalep);
            result.add(savedHedeflerAidiyatTalep);
        }
        return result;
    }


    public List<MahkemeKararTalepDTO> findAll() {
        List<MahkemeKararTalep> mahkemeKararTalepList = dbMahkemeKararTalepService.findAll();
        return mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList);
    }

    /**
     * Get mahkeme karar talep by ID
     * @param id Mahkeme karar talep ID
     * @return MahkemeKararTalepDTO
     * @throws ResponseStatusException if not found
     */

    public MahkemeKararTalepDTO findById(Long id) {
        return dbMahkemeKararTalepService.findById(id)
                .map(mahkemeKararTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));
    }

    /**
     * Get mahkeme karar talep records by evrak ID
     * @param evrakId Evrak ID
     * @return List of MahkemeKararTalepDTO
     */

    public List<MahkemeKararTalepDTO> findByEvrakId(Long evrakId) {
        List<MahkemeKararTalep> mahkemeKararTalepList = dbMahkemeKararTalepService.findByEvrakId(evrakId);
        return mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList);
    }


    /**
     * Get paginated mahkeme karar talep records
     * @param pageable Pageable
     * @return Page of MahkemeKararTalepDTO
     */

    public Page<MahkemeKararTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeKararTalep> mahkemeKararTalepPage = dbMahkemeKararTalepService.findAll(pageable);
        List<MahkemeKararTalepDTO> dtoList = mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeKararTalepPage.getTotalElements());
    }

    /**
     * Create new mahkeme karar talep
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Created MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO create(MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        // Check if mahkeme karar already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
            mahkemeKararTalepDTO.getMahkemeAdi() != null &&
            mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null) {
            // TODO
        }

        MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepMapper.toEntity(mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.save(mahkemeKararTalep);
        log.info("Mahkeme karar talep oluşturuldu: {}", mahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(mahkemeKararTalep);
    }

    /**
     * Update mahkeme karar talep
     * @param id Mahkeme karar talep ID
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Updated MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO update(Long id, MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        MahkemeKararTalep existingMahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));

        // Check if mahkeme karar is being changed and already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
            mahkemeKararTalepDTO.getMahkemeAdi() != null &&
            mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null &&
            (!mahkemeKararTalepDTO.getMahkemeKararNo().equals(existingMahkemeKararTalep.getMahkemeKararNo()) ||
             !mahkemeKararTalepDTO.getMahkemeAdi().equals(existingMahkemeKararTalep.getMahkemeAdi()) ||
             !mahkemeKararTalepDTO.getMahkemeIlIlceKodu().equals(existingMahkemeKararTalep.getMahkemeIlIlceKodu()))) {

            // TODO
        }

        MahkemeKararTalep updatedMahkemeKararTalep = mahkemeKararTalepMapper.updateEntityFromDto(existingMahkemeKararTalep, mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.update(updatedMahkemeKararTalep);
        log.info("Mahkeme karar talep güncellendi: {}", updatedMahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(updatedMahkemeKararTalep);
    }

    /**
     * Delete mahkeme karar talep
     * @param id Mahkeme karar talep ID
     */

    public void delete(Long id) {
        MahkemeKararTalep mahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));

        dbMahkemeKararTalepService.delete(mahkemeKararTalep);
        log.info("Mahkeme karar talep silindi: {}", id);
    }
}
