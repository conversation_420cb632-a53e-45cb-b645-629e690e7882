package iym.makos.controller.filters;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.model.entity.oracle.makos.log.MakosKararRequestLog;
import iym.common.service.db.DbMakosKararRequestLogService;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.controller.filters.util.CachedBodyHttpServletRequest;
import iym.common.util.HttpUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StreamUtils;
import jakarta.servlet.http.Part;
import java.nio.charset.StandardCharsets;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import org.springframework.web.util.ContentCachingResponseWrapper;

/**
 * Filter for logging court decision requests in MAKOS module
 * Logs all mahkeme karar related requests and responses for audit and monitoring
 */
@Slf4j
public class MakosKararRequestLogFilter implements Filter {

    private final DbMakosKararRequestLogService makosKararRequestLogService;

    public MakosKararRequestLogFilter(DbMakosKararRequestLogService makosKararRequestLogService) {
        this.makosKararRequestLogService = makosKararRequestLogService;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        log.debug("MAKOS Karar Request Log Filter started for request");

        //----------  LOG REQUEST  ----------
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetailsImpl = null;

        if (authentication != null && !authentication.getPrincipal().equals("anonymousUser")) {
            userDetailsImpl = (UserDetailsImpl) authentication.getPrincipal();
        }

        HttpServletRequest originalRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse originalResponse = (HttpServletResponse) servletResponse;

        boolean isMultipart = originalRequest.getContentType() != null &&
                              originalRequest.getContentType().toLowerCase().startsWith("multipart/");

        String jsonString = null;
        CachedBodyHttpServletRequest cachedRequest;
        ServletRequest requestForChain;
        ContentCachingResponseWrapper cachedResponse = new ContentCachingResponseWrapper(originalResponse);
        ServletResponse responseForChain = cachedResponse;

        if (isMultipart) {
            // Use the original request for multipart parsing
            try {
                for (Part part : originalRequest.getParts()) {
                    String str = part.getName();
                    if ("jsonData".equals(part.getName())) { // adjust name as needed
                        jsonString = new String(part.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
                        break;
                    }
                }
                // If jsonString is still null, jsonData part was not found
                if (jsonString == null) {
                    originalResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    originalResponse.setContentType("application/json");
                    originalResponse.getWriter().write("{\"error\":\"jsonData named parameter required\"}");
                    originalResponse.getWriter().flush();
                    return;
                }
            } catch (Exception e) {
                log.warn("Could not extract jsonData part from multipart request", e);
            }
            // Pass the original request to the filter chain
            requestForChain = originalRequest;
        } else {
            // Use cached wrapper for non-multipart
            cachedRequest = new CachedBodyHttpServletRequest(originalRequest);
            byte[] inputStreamBytes = StreamUtils.copyToByteArray(cachedRequest.getInputStream());
            if (inputStreamBytes.length > 0) {
                jsonString = new String(inputStreamBytes, StandardCharsets.UTF_8);
            }
            requestForChain = cachedRequest;
        }

        // Extract mahkeme karar details from MahkemeKararRequest structure
        String mahkemeKararNo = null;
        String sorusturmaNo = null;
        String evrakNo = null;
        String targetId = null;
        if (jsonString != null) {
            try {
                Map<String, Object> jsonRequest = new ObjectMapper().readValue(jsonString, Map.class);

                // Extract id (targetId)
                if (jsonRequest.containsKey("id")) {
                    targetId = String.valueOf(jsonRequest.get("id"));
                }

                // Extract mahkemeKararNo and sorusturmaNo
                if (jsonRequest.containsKey("mahkemeKararBilgisi")) {
                    Map<String, Object> mahkemeKararBilgisi = (Map<String, Object>) jsonRequest.get("mahkemeKararBilgisi");
                    if (mahkemeKararBilgisi != null && mahkemeKararBilgisi.containsKey("mahkemeKararDetay")) {
                        Map<String, Object> mahkemeKararDetay = (Map<String, Object>) mahkemeKararBilgisi.get("mahkemeKararDetay");
                        if (mahkemeKararDetay != null) {
                            mahkemeKararNo = (String) mahkemeKararDetay.get("mahkemeKararNo");
                            sorusturmaNo = (String) mahkemeKararDetay.get("sorusturmaNo");
                        }
                    }
                }

                // Extract evrakNo
                if (jsonRequest.containsKey("evrakDetay")) {
                    Map<String, Object> evrakDetay = (Map<String, Object>) jsonRequest.get("evrakDetay");
                    if (evrakDetay != null) {
                        evrakNo = (String) evrakDetay.get("evrakNo");
                    }
                }
            } catch (Exception e) {
                log.warn("Could not parse JSON from request body or part", e);
            }
        }

        // requested api function
        URI uri = null;
        try {
            uri = new URI(originalRequest.getRequestURL().toString());
        } catch (URISyntaxException e) {
            log.error("Can't get uri from {}", originalRequest.getRequestURL().toString(), e);
            throw new RuntimeException(e);
        }

        // client ip address info
        String ipAddress = HttpUtils.getClientIpAddress(originalRequest);

        // Generate unique request ID for this request
        UUID requestId = UUID.randomUUID();

        // build karar request log with request information
        MakosKararRequestLog kararRequestLog = new MakosKararRequestLog();
        kararRequestLog.setRequestId(requestId);
        kararRequestLog.setRequestURL(originalRequest.getRequestURL().toString());
        kararRequestLog.setUsername(userDetailsImpl == null ? "anonymousUser" : userDetailsImpl.getUsername());
        kararRequestLog.setActingUsername(userDetailsImpl == null ? null : userDetailsImpl.getActingUserName());
        kararRequestLog.setUserIp(ipAddress);
        kararRequestLog.setTargetId(targetId);
        kararRequestLog.setMahkemeKararNo(mahkemeKararNo);
        kararRequestLog.setSorusturmaNo(sorusturmaNo);
        kararRequestLog.setRequestTime(LocalDateTime.now());
        kararRequestLog.setEvrakNo(evrakNo);
        
        // Store request body (no length limit, CLOB)
        if (jsonString != null && jsonString.length() > 0) {
            kararRequestLog.setRequestBody(jsonString);
        }

        // save to db, then use the same object to write response info after filter chain
        makosKararRequestLogService.save(kararRequestLog);
        log.debug("MAKOS Karar Request Log Filter ended for request");

        //----------  DO FILTER CHAIN  ----------
        filterChain.doFilter(requestForChain, responseForChain);

        //----------  LOG RESPONSE  ----------
        log.debug("MAKOS Karar Request Log Filter started for response.");
        kararRequestLog.setResponseTime(LocalDateTime.now());
        kararRequestLog.setResponseCode(cachedResponse.getStatus());
        String responseBody = new String(cachedResponse.getContentAsByteArray(), cachedResponse.getCharacterEncoding());
        if (responseBody != null && responseBody.length() > 0) {
            kararRequestLog.setResponseBody(responseBody);
        }
        // Write the cached response body to the actual response output stream
        cachedResponse.copyBodyToResponse();
        makosKararRequestLogService.save(kararRequestLog);
        log.debug("MAKOS Karar Request Log Filter ended for response");
    }
}
