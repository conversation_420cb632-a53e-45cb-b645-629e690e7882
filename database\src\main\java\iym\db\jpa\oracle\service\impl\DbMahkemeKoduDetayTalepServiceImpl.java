package iym.db.jpa.oracle.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.oracle.iym.MahkemeKoduDetayTalep;
import iym.common.service.db.DbMahkemeKoduDetayTalepService;
import iym.db.jpa.oracle.dao.MahkemeKoduDetayTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKoduDetayTalepServiceImpl extends GenericDbServiceImpl<MahkemeKoduDetayTalep, Long> implements DbMahkemeKoduDetayTalepService {

    private final MahkemeKoduDetayTalepRepo mahkemeKoduDetayTalepRepo;

    @Autowired
    public DbMahkemeKoduDetayTalepServiceImpl(MahkemeKoduDetayTalepRepo repository) {
        super(repository);
        this.mahkemeKoduDetayTalepRepo = repository;
    }

    @Override
    public Optional<MahkemeKoduDetayTalep> findByMahkemeKararDetayId(Long mahkemeKararDetayId) {
        return mahkemeKoduDetayTalepRepo.findByMahkemeKararDetayId(mahkemeKararDetayId);
    }
}
