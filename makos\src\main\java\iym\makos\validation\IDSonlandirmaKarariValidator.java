package iym.makos.validation;

import iym.common.model.api.Evrak<PERSON>urum;
import iym.common.model.api.HedefTip;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.model.entity.oracle.iym.Hedefler;
import iym.common.model.entity.oracle.iym.MahkemeKarar;
import iym.common.model.entity.oracle.iym.SucTipi;
import iym.common.service.db.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDSonlandirmaKarariRequest;
import iym.makos.model.api.HedefDetayID;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Optional;

@Service
@Slf4j
public class IDSonlandirmaKarariValidator extends MahkemeKararRequestValidatorBase<IDSonlandirmaKarariRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public IDSonlandirmaKarariValidator(DbMahkemeKararService dbMahkemeKararService,
                                        DbHedeflerService dbHedeflerService,
                                        DbSucTipiService dbSucTipiService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    public ValidationResult validate(IDSonlandirmaKarariRequest request) {

        try{

            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);
            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();

            //Ohal Kontrolu
            if (LocalDate.now().isAfter(IDYeniKararValidator.OHAL_DATE) && kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)) {
                validationResult.addFailedReason("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
            }

            for (HedefDetayID hedefDetayID : request.getHedefDetayListesi()) {

                if (!CommonUtils.isNullOrEmpty(hedefDetayID.getCanakNo())) {
                    validationResult.addFailedReason("Sonlandirma kararında CANAK numarası girilemez.");
                }

                if (hedefDetayID.getUzatmaSayisi() != null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda uzatma sayisi dolu olamaz!");
                }

                MahkemeKararDetay iliskiliMahkemeKararDetay = hedefDetayID.getIlgiliMahkemeKararDetayi();
                if (iliskiliMahkemeKararDetay == null) {

                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format("Güncelle Konu Mahkeme Karar Bulunamadı: Mahheme İl/İlçe Kodu: %s Mahkeme Kodu: %s, Karar No: %s, Soruşturma No :%s"
                                , iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {
                        String hedefNo = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefNo();
                        HedefTip hedefTipi = hedefDetayID.getHedefNoAdSoyad().getHedef().getHedefTip();

                        //Sistemde bu hedef var mi?
                        Optional<Hedefler> existingHedefBilgisiOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKararOpt.get().getId(), hedefNo, hedefTipi);

                        if (existingHedefBilgisiOpt.isEmpty()) {
                            validationResult.addFailedReason(hedefNo + " numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
                        }
                    }
                }
            }

            //Suç tiplerini kontrol et
            if (request.getMahkemeSucTipiKodlari() != null && !request.getMahkemeSucTipiKodlari().isEmpty()) {
                for (String sucTipiKodu : request.getMahkemeSucTipiKodlari()) {
                    Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                    if (sucTipiOpt.isEmpty()) {
                        validationResult.addFailedReason(sucTipiKodu + " kodlu suç tipi bulunamadı.");
                    }
                }
            }

            //Aidiyatlar
            if (request.getMahkemeAidiyatKodlari() != null && !request.getMahkemeAidiyatKodlari().isEmpty()) {

                for (String aidiyatKod : request.getMahkemeAidiyatKodlari()) {
                    if (evrakKurum == EvrakKurum.JANDARMA) {
                        if (kararTipi == MahkemeKararTip.ADLI_HAKIM_KARARI
                                || kararTipi == MahkemeKararTip.ADLI_YAZILI_EMIR
                                || kararTipi == MahkemeKararTip.ADLI_KHK_YAZILI_EMIR) {
                            if (!aidiyatKod.startsWith("JA")) {
                                validationResult.addFailedReason(aidiyatKod + " kurumunuz Adli Karar Aidiyatları JA ile baslamalidir.");
                            }
                        } else if (kararTipi == MahkemeKararTip.ONLEYICI_HAKIM_KARARI
                                || kararTipi == MahkemeKararTip.ONLEYICI_YAZILI_EMIR) {
                            if (!(aidiyatKod.startsWith("Jİ") || aidiyatKod.startsWith("JG") || aidiyatKod.startsWith("JK") || aidiyatKod.startsWith("JT") || aidiyatKod.startsWith("JB"))) {
                                validationResult.addFailedReason(aidiyatKod + " kurumunuz İstihbari Karar Aidiyatları Jİ ile baslamalidir.");
                            }
                        }
                    } else if (evrakKurum == EvrakKurum.MIT) {
                        if (!((aidiyatKod.length() == 7 && aidiyatKod.startsWith("MT"))
                                || (aidiyatKod.length() == 4 && aidiyatKod.startsWith("MİT")))) {
                            validationResult.addFailedReason("Kurumunuz Karar Aidiyatları MT veya MİT ile baslamalidir.");
                        }
                    } else if (evrakKurum == EvrakKurum.EGMIDB) {
                        if (!((aidiyatKod.length() == 8 && aidiyatKod.startsWith("Y")))) {
                            validationResult.addFailedReason("Kurumunuz Karar Aidiyatları Y ile baslamali ve 8 karakter olmalıdır.");
                            break;
                        }
                    }
                }
            }


            return validationResult;
        }catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

}

