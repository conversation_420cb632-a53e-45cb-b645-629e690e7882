package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.EvrakGelenKurumlar;
import iym.makos.dto.EvrakGelenKurumlarDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class EvrakGelenKurumlarMapperTest {

    private EvrakGelenKurumlarMapper evrakGelenKurumlarMapper;
    private EvrakGelenKurumlar evrakGelenKurumlar;
    private EvrakGelenKurumlarDTO evrakGelenKurumlarDTO;

    @BeforeEach
    void setUp() {
        evrakGelenKurumlarMapper = new EvrakGelenKurumlarMapper();

        evrakGelenKurumlar = EvrakGelenKurumlar.builder()
                .id(1L)
                .kurumKod("01")
                .kurumAdi("ADALET BAKANLIĞI")
                .kurum("ADALET BAKANLIĞI")
                .idx(1L)
                .build();

        evrakGelenKurumlarDTO = EvrakGelenKurumlarDTO.builder()
                .id(1L)
                .kurumKod("01")
                .kurumAdi("ADALET BAKANLIĞI")
                .kurum("ADALET BAKANLIĞI")
                .idx(1L)
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        EvrakGelenKurumlarDTO result = evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(evrakGelenKurumlar.getId());
        assertThat(result.getKurumKod()).isEqualTo(evrakGelenKurumlar.getKurumKod());
        assertThat(result.getKurumAdi()).isEqualTo(evrakGelenKurumlar.getKurumAdi());
        assertThat(result.getKurum()).isEqualTo(evrakGelenKurumlar.getKurum());
        assertThat(result.getIdx()).isEqualTo(evrakGelenKurumlar.getIdx());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        EvrakGelenKurumlarDTO result = evrakGelenKurumlarMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        EvrakGelenKurumlar result = evrakGelenKurumlarMapper.toEntity(evrakGelenKurumlarDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(evrakGelenKurumlarDTO.getId());
        assertThat(result.getKurumKod()).isEqualTo(evrakGelenKurumlarDTO.getKurumKod());
        assertThat(result.getKurumAdi()).isEqualTo(evrakGelenKurumlarDTO.getKurumAdi());
        assertThat(result.getKurum()).isEqualTo(evrakGelenKurumlarDTO.getKurum());
        assertThat(result.getIdx()).isEqualTo(evrakGelenKurumlarDTO.getIdx());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        EvrakGelenKurumlar result = evrakGelenKurumlarMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        EvrakGelenKurumlar existingEntity = EvrakGelenKurumlar.builder()
                .id(1L)
                .kurumKod("01")
                .kurumAdi("ADALET BAKANLIĞI")
                .kurum("ADALET BAKANLIĞI")
                .idx(1L)
                .build();

        EvrakGelenKurumlarDTO updatedDto = EvrakGelenKurumlarDTO.builder()
                .id(2L) // Should not be updated
                .kurumKod("02")
                .kurumAdi("İÇİŞLERİ BAKANLIĞI")
                .kurum("İÇİŞLERİ BAKANLIĞI")
                .idx(2L)
                .build();

        // When
        EvrakGelenKurumlar result = evrakGelenKurumlarMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L); // Should not be updated
        assertThat(result.getKurumKod()).isEqualTo("02");
        assertThat(result.getKurumAdi()).isEqualTo("İÇİŞLERİ BAKANLIĞI");
        assertThat(result.getKurum()).isEqualTo("İÇİŞLERİ BAKANLIĞI");
        assertThat(result.getIdx()).isEqualTo(2L);
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        EvrakGelenKurumlar existingEntity = EvrakGelenKurumlar.builder()
                .id(1L)
                .kurumKod("01")
                .kurumAdi("ADALET BAKANLIĞI")
                .kurum("ADALET BAKANLIĞI")
                .idx(1L)
                .build();

        // When
        EvrakGelenKurumlar result = evrakGelenKurumlarMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        EvrakGelenKurumlar result = evrakGelenKurumlarMapper.updateEntityFromDto(null, evrakGelenKurumlarDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<EvrakGelenKurumlar> entityList = Arrays.asList(evrakGelenKurumlar, evrakGelenKurumlar);

        // When
        List<EvrakGelenKurumlarDTO> result = evrakGelenKurumlarMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(evrakGelenKurumlar.getId());
        assertThat(result.get(1).getId()).isEqualTo(evrakGelenKurumlar.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<EvrakGelenKurumlarDTO> result = evrakGelenKurumlarMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<EvrakGelenKurumlarDTO> dtoList = Arrays.asList(evrakGelenKurumlarDTO, evrakGelenKurumlarDTO);

        // When
        List<EvrakGelenKurumlar> result = evrakGelenKurumlarMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(evrakGelenKurumlarDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(evrakGelenKurumlarDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<EvrakGelenKurumlar> result = evrakGelenKurumlarMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
