package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.EvrakFiles;
import iym.makos.dto.EvrakFilesDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Hedefler entity and DTO
 */
@Component
public class EvrakFilesMapper {

    /**
     * Convert entity to DTO
     * @param entity EvrakFiles entity
     * @return EvrakFilesDTO
     */
    public EvrakFilesDTO toDto(EvrakFiles entity) {
        if (entity == null) {
            return null;
        }

        return EvrakFilesDTO.builder()
                .id(entity.getId())
                .evrakId(entity.getEvrakId())
                .fileName(entity.getFileName())
                .siraNo(entity.getSiraNo())
                .silindi(entity.getSilindi() != null && entity.getSilindi().equals("E"))
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto EvrakFilesDTO
     * @return EvrakFiles entity
     */
    public EvrakFiles toEntity(EvrakFilesDTO dto) {
        if (dto == null) {
            return null;
        }

        return EvrakFiles.builder()
                .id(dto.getId())
                .evrakId(dto.getEvrakId())
                .fileName(dto.getFileName())
                .siraNo(dto.getSiraNo())
                .silindi(dto.isSilindi() ? "E" : "H")
                .build();
    }



    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of EvrakFiles entities
     * @return List of EvrakFilesDTO
     */
    public List<EvrakFilesDTO> toDtoList(List<EvrakFiles> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of EvrakFilesDTO
     * @return List of EvrakFiles entities
     */
    public List<EvrakFiles> toEntityList(List<EvrakFilesDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
