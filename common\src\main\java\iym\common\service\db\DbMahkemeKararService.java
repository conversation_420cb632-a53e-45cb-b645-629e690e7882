package iym.common.service.db;

import iym.common.model.entity.oracle.iym.MahkemeKarar;

import java.util.List;
import java.util.Optional;


public interface DbMahkemeKararService extends GenericDbService<MahkemeKarar, Long> {

    //Optional<MahkemeKarar> findById(Long evrakId);

    List<MahkemeKarar> findByEvrakId(Long evrakId);

    Optional<MahkemeKarar> findBy(
            String mahkemeIlIlceKodu,
            String mahkemeKodu,
            String mahkemeKararNo,
            String sorusturmaNo
    );
}
