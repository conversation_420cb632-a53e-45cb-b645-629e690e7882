package iym.common.model.entity.oracle.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for HEDEF_TIPLERI table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HedefTipleri")
@Table(name = "HEDEF_TIPLERI")
public class HedefTipleri implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Column(name = "ID")
    private Long id;

    @Id
    @Column(name = "HEDEF_KODU", nullable = false)
    @NotNull
    private Long hedefKodu;

    @Column(name = "HEDEF_TIPI", length = 25)
    @Size(max = 25)
    private String hedefTipi;

    @Column(name = "SONLANDIRMAMI", length = 1, columnDefinition = "CHAR(1)")
    @Size(max = 1)
    private String sonlandirmami;

    @Column(name = "KARSILIGI")
    private Long karsiligi;

    @Column(name = "SNO")
    private Long sno;

    @Column(name = "HEDEF_TANIM", length = 16)
    @Size(max = 16)
    private String hedefTanim;

    @Column(name = "DURUM", length = 8)
    @Size(max = 8)
    private String durum;

    @Column(name = "HITAP_TIP", length = 8)
    @Size(max = 8)
    private String hitapTip;

    @Column(name = "HITAP_ICERIK_TIP", length = 8)
    @Size(max = 8)
    private String hitapIcerikTip;

    @Column(name = "HITAP_ICINDEMI", length = 8)
    @Size(max = 8)
    private String hitapIcindemi;

    @Column(name = "HITAP_EH", length = 1, columnDefinition = "CHAR(1)")
    @Size(max = 1)
    private String hitapEh;

    @Column(name = "MINL")
    private Long minl;

    @Column(name = "MAXL")
    private Long maxl;

    @Column(name = "IMHA_YAPILSINMI", length = 8)
    @Size(max = 8)
    private String imhaYapilsinmi;

    @Column(name = "TASINABILIRMI", length = 1)
    @Size(max = 1)
    private String tasinabilirmi;

    @Column(name = "AKTIFMI")
    private Long aktifmi;

    @Column(name = "HITAPA_GONDERILECEKMI", precision = 1, scale = 0)
    private Long hitapaGonderilecekmi;
}
