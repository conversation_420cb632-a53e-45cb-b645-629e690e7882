package iym.makos.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Temporary controller to test property loading
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class PropertiesTestController {

    @Autowired
    private Environment environment;

    @GetMapping("/properties")
    public ResponseEntity<Map<String, Object>> testProperties() {
        Map<String, Object> result = new HashMap<>();
        
        log.info("Testing property loading...");
        
        // Test Oracle datasource properties
        String[] oracleProps = {
            "spring.datasource.oracle.jdbcUrl",
            "spring.datasource.oracle.url", // also check the old property name
            "spring.datasource.oracle.driverClassName",
            "spring.datasource.oracle.username",
            "spring.datasource.oracle.password",
            "spring.datasource.oracle.hikari.poolName",
            "spring.datasource.oracle.hikari.maximumPoolSize"
        };
        
        Map<String, String> oracleProperties = new HashMap<>();
        for (String prop : oracleProps) {
            String value = environment.getProperty(prop);
            oracleProperties.put(prop, value != null ? value : "NOT_FOUND");
            log.info("Property {}: {}", prop, value != null ? value : "NOT_FOUND");
        }
        
        result.put("oracleProperties", oracleProperties);
        
        // Test active profiles
        String[] activeProfiles = environment.getActiveProfiles();
        result.put("activeProfiles", activeProfiles);
        log.info("Active profiles: {}", String.join(", ", activeProfiles));
        
        // Test default profiles
        String[] defaultProfiles = environment.getDefaultProfiles();
        result.put("defaultProfiles", defaultProfiles);
        log.info("Default profiles: {}", String.join(", ", defaultProfiles));
        
        return ResponseEntity.ok(result);
    }
}
