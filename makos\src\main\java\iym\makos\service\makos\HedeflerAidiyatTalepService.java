package iym.makos.service.makos;

import iym.common.model.entity.oracle.iym.HedeflerAidiyatTalep;
import iym.common.service.db.DbHedeflerAidiyatTalepService;
import iym.makos.dto.HedeflerAidiyatTalepDTO;
import iym.makos.mapper.HedeflerAidiyatTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service for HedeflerAidiyatTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HedeflerAidiyatTalepService {

    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    private final HedeflerAidiyatTalepMapper hedeflerAidiyatTalepMapper;



    /**
     * Get all hedefler aidiyat talep records
     * @return List of HedeflerAidiyatTalepDTO
     */
    public List<HedeflerAidiyatTalepDTO> findAll() {
        List<HedeflerAidiyatTalep> hedeflerAidiyatTalepList = dbHedeflerAidiyatTalepService.findAll();
        return hedeflerAidiyatTalepMapper.toDtoList(hedeflerAidiyatTalepList);
    }

    /**
     * Get all hedefler aidiyat talep records with pagination
     * @param pageable Pagination information
     * @return Page of HedeflerAidiyatTalepDTO
     */
    public Page<HedeflerAidiyatTalepDTO> findAll(Pageable pageable) {
        Page<HedeflerAidiyatTalep> hedeflerAidiyatTalepPage = dbHedeflerAidiyatTalepService.findAll(pageable);
        List<HedeflerAidiyatTalepDTO> dtoList = hedeflerAidiyatTalepMapper.toDtoList(hedeflerAidiyatTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, hedeflerAidiyatTalepPage.getTotalElements());
    }

    /**
     * Get hedefler aidiyat talep by id
     * @param id HedeflerAidiyatTalep id
     * @return HedeflerAidiyatTalepDTO
     */
    public HedeflerAidiyatTalepDTO findById(Long id) {
        HedeflerAidiyatTalep hedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler aidiyat talep bulunamadı: " + id));
        return hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep);
    }

    /**
     * Get hedefler aidiyat talep by hedef id
     * @param hedefId Hedef id
     * @return List of HedeflerAidiyatTalepDTO
     */
    public List<HedeflerAidiyatTalepDTO> findByHedefId(Long hedefId) {
        List<HedeflerAidiyatTalep> hedeflerAidiyatTalepList = dbHedeflerAidiyatTalepService.findByHedefTalepId(hedefId);
        return hedeflerAidiyatTalepMapper.toDtoList(hedeflerAidiyatTalepList);
    }



    /**
     * Get hedefler aidiyat talep by tarih between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HedeflerAidiyatTalepDTO
     */
    public List<HedeflerAidiyatTalepDTO> findByTarihBetween(Date startDate, Date endDate) {
        List<HedeflerAidiyatTalep> hedeflerAidiyatTalepList = dbHedeflerAidiyatTalepService.findByTarihBetween(startDate, endDate);
        return hedeflerAidiyatTalepMapper.toDtoList(hedeflerAidiyatTalepList);
    }

    /**
     * Get hedefler aidiyat talep by hedef id and aidiyat kod
     * @param hedefId Hedef id
     * @param aidiyatKod Aidiyat kod
     * @return HedeflerAidiyatTalepDTO
     */
    public HedeflerAidiyatTalepDTO findByHedefTalepIdAndAidiyatKod(Long hedefId, String aidiyatKod) {
        HedeflerAidiyatTalep hedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(hedefId, aidiyatKod)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, 
                        "Hedefler aidiyat talep bulunamadı: Hedef ID: " + hedefId + 
                        ", Aidiyat Kod: " + aidiyatKod));
        return hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep);
    }



    /**
     * Create new hedefler aidiyat talep
     * @param hedeflerAidiyatTalepDTO HedeflerAidiyatTalepDTO
     * @return Created HedeflerAidiyatTalepDTO
     */
    public HedeflerAidiyatTalepDTO create(HedeflerAidiyatTalepDTO hedeflerAidiyatTalepDTO) {
        // Check if hedefler aidiyat talep already exists
        if (hedeflerAidiyatTalepDTO.getHedefTalepId() != null && hedeflerAidiyatTalepDTO.getAidiyatKod() != null) {
            Optional<HedeflerAidiyatTalep> existingHedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(
                    hedeflerAidiyatTalepDTO.getHedefTalepId(), hedeflerAidiyatTalepDTO.getAidiyatKod());
                    
            if (existingHedeflerAidiyatTalep.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu hedefler aidiyat talebi zaten mevcut: Hedef ID: " + hedeflerAidiyatTalepDTO.getHedefTalepId() +
                    ", Aidiyat Kod: " + hedeflerAidiyatTalepDTO.getAidiyatKod());
            }
        }

        HedeflerAidiyatTalep hedeflerAidiyatTalep = hedeflerAidiyatTalepMapper.toEntity(hedeflerAidiyatTalepDTO);
        dbHedeflerAidiyatTalepService.save(hedeflerAidiyatTalep);
        log.info("Hedefler aidiyat talep oluşturuldu: {}", hedeflerAidiyatTalep.getId());
        return hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep);
    }

    /**
     * Update existing hedefler aidiyat talep
     * @param id HedeflerAidiyatTalep id
     * @param hedeflerAidiyatTalepDTO HedeflerAidiyatTalepDTO
     * @return Updated HedeflerAidiyatTalepDTO
     */
    public HedeflerAidiyatTalepDTO update(Long id, HedeflerAidiyatTalepDTO hedeflerAidiyatTalepDTO) {
        HedeflerAidiyatTalep existingHedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler aidiyat talep bulunamadı: " + id));

        // Check if updated hedefler aidiyat talep would conflict with an existing one
        if (hedeflerAidiyatTalepDTO.getHedefTalepId() != null && hedeflerAidiyatTalepDTO.getAidiyatKod() != null) {
            Optional<HedeflerAidiyatTalep> conflictingHedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(
                    hedeflerAidiyatTalepDTO.getHedefTalepId(), hedeflerAidiyatTalepDTO.getAidiyatKod());
                    
            if (conflictingHedeflerAidiyatTalep.isPresent() && !conflictingHedeflerAidiyatTalep.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu hedefler aidiyat talebi zaten mevcut: Hedef ID: " + hedeflerAidiyatTalepDTO.getHedefTalepId() +
                    ", Aidiyat Kod: " + hedeflerAidiyatTalepDTO.getAidiyatKod());
            }
        }

        HedeflerAidiyatTalep updatedHedeflerAidiyatTalep = hedeflerAidiyatTalepMapper.updateEntityFromDto(existingHedeflerAidiyatTalep, hedeflerAidiyatTalepDTO);
        dbHedeflerAidiyatTalepService.update(updatedHedeflerAidiyatTalep);
        log.info("Hedefler aidiyat talep güncellendi: {}", updatedHedeflerAidiyatTalep.getId());
        return hedeflerAidiyatTalepMapper.toDto(updatedHedeflerAidiyatTalep);
    }

    /**
     * Delete hedefler aidiyat talep
     * @param id HedeflerAidiyatTalep id
     */
    public void delete(Long id) {
        HedeflerAidiyatTalep hedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler aidiyat talep bulunamadı: " + id));
        dbHedeflerAidiyatTalepService.delete(hedeflerAidiyatTalep);
        log.info("Hedefler aidiyat talep silindi: {}", id);
    }
}
