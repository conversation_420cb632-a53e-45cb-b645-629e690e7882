package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.MahkemeHedeflerAidiyatTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.MahkemeHedeflerAidiyatTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class MahkemeHedeflerAidiyatTalepMapperTest {

    private MahkemeHedeflerAidiyatTalepMapper mahkemeHedeflerAidiyatTalepMapper;
    private MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep;
    private MahkemeHedeflerAidiyatTalepDTO mahkemeHedeflerAidiyatTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        mahkemeHedeflerAidiyatTalepMapper = new MahkemeHedeflerAidiyatTalepMapper();
        testDate = new Date();

        mahkemeHedeflerAidiyatTalep = MahkemeHedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .mahkemeKararId(200L)
                .durumu("AKTIF")
                .kullaniciId(300L)
                .build();

        mahkemeHedeflerAidiyatTalepDTO = MahkemeHedeflerAidiyatTalepDTO.builder()
                .id(1L)
                .hedefId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .mahkemeKararId(200L)
                .durumu("AKTIF")
                .kullaniciId(300L)
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        MahkemeHedeflerAidiyatTalepDTO result = mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeHedeflerAidiyatTalep.getId());
        assertThat(result.getHedefId()).isEqualTo(mahkemeHedeflerAidiyatTalep.getHedefId());
        assertThat(result.getAidiyatKod()).isEqualTo(mahkemeHedeflerAidiyatTalep.getAidiyatKod());
        assertThat(result.getTarih()).isEqualTo(mahkemeHedeflerAidiyatTalep.getTarih());
        assertThat(result.getMahkemeKararId()).isEqualTo(mahkemeHedeflerAidiyatTalep.getMahkemeKararId());
        assertThat(result.getDurumu()).isEqualTo(mahkemeHedeflerAidiyatTalep.getDurumu());
        assertThat(result.getKullaniciId()).isEqualTo(mahkemeHedeflerAidiyatTalep.getKullaniciId());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeHedeflerAidiyatTalepDTO result = mahkemeHedeflerAidiyatTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        MahkemeHedeflerAidiyatTalep result = mahkemeHedeflerAidiyatTalepMapper.toEntity(mahkemeHedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getId());
        assertThat(result.getHedefId()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getHedefId());
        assertThat(result.getAidiyatKod()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod());
        assertThat(result.getTarih()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getTarih());
        assertThat(result.getMahkemeKararId()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId());
        assertThat(result.getDurumu()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getDurumu());
        assertThat(result.getKullaniciId()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getKullaniciId());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        MahkemeHedeflerAidiyatTalep result = mahkemeHedeflerAidiyatTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        MahkemeHedeflerAidiyatTalep existingEntity = MahkemeHedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .mahkemeKararId(200L)
                .durumu("AKTIF")
                .kullaniciId(300L)
                .build();

        MahkemeHedeflerAidiyatTalepDTO updatedDto = MahkemeHedeflerAidiyatTalepDTO.builder()
                .id(1L)
                .hedefId(101L)
                .aidiyatKod("AIDIYAT2")
                .tarih(testDate)
                .mahkemeKararId(201L)
                .durumu("PASIF")
                .kullaniciId(301L)
                .build();

        // When
        MahkemeHedeflerAidiyatTalep result = mahkemeHedeflerAidiyatTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getHedefId()).isEqualTo(101L);
        assertThat(result.getAidiyatKod()).isEqualTo("AIDIYAT2");
        assertThat(result.getTarih()).isEqualTo(testDate);
        assertThat(result.getMahkemeKararId()).isEqualTo(201L);
        assertThat(result.getDurumu()).isEqualTo("PASIF");
        assertThat(result.getKullaniciId()).isEqualTo(301L);
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        MahkemeHedeflerAidiyatTalep existingEntity = MahkemeHedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .mahkemeKararId(200L)
                .durumu("AKTIF")
                .kullaniciId(300L)
                .build();

        // When
        MahkemeHedeflerAidiyatTalep result = mahkemeHedeflerAidiyatTalepMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeHedeflerAidiyatTalep result = mahkemeHedeflerAidiyatTalepMapper.updateEntityFromDto(null, mahkemeHedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<MahkemeHedeflerAidiyatTalep> entityList = Arrays.asList(mahkemeHedeflerAidiyatTalep, mahkemeHedeflerAidiyatTalep);

        // When
        List<MahkemeHedeflerAidiyatTalepDTO> result = mahkemeHedeflerAidiyatTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(mahkemeHedeflerAidiyatTalep.getId());
        assertThat(result.get(1).getId()).isEqualTo(mahkemeHedeflerAidiyatTalep.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<MahkemeHedeflerAidiyatTalepDTO> result = mahkemeHedeflerAidiyatTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<MahkemeHedeflerAidiyatTalepDTO> dtoList = Arrays.asList(mahkemeHedeflerAidiyatTalepDTO, mahkemeHedeflerAidiyatTalepDTO);

        // When
        List<MahkemeHedeflerAidiyatTalep> result = mahkemeHedeflerAidiyatTalepMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(mahkemeHedeflerAidiyatTalepDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<MahkemeHedeflerAidiyatTalep> result = mahkemeHedeflerAidiyatTalepMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
