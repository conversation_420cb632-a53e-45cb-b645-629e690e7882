package iym.makos.service.makos;

import iym.common.model.entity.oracle.iym.CanakNumaralar;
import iym.common.service.db.DbCanakNumaralarService;
import iym.makos.dto.CanakNumaralarDTO;
import iym.makos.mapper.CanakNumaralarMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;

/**
 * Service for CanakNumaralar operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CanakNumaralarService {

    private final DbCanakNumaralarService dbCanakNumaralarService;
    private final CanakNumaralarMapper canakNumaralarMapper;

    /**
     * Get all canak numaralar records
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findAll() {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findAll();
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get all canak numaralar records with pagination
     * @param pageable Pagination information
     * @return Page of CanakNumaralarDTO
     */
    public Page<CanakNumaralarDTO> findAll(Pageable pageable) {
        Page<CanakNumaralar> canakNumaralarPage = dbCanakNumaralarService.findAll(pageable);
        List<CanakNumaralarDTO> dtoList = canakNumaralarMapper.toDtoList(canakNumaralarPage.getContent());
        return new PageImpl<>(dtoList, pageable, canakNumaralarPage.getTotalElements());
    }

    /**
     * Get canak numaralar by id
     * @param id CanakNumaralar id
     * @return CanakNumaralarDTO
     */
    public CanakNumaralarDTO findById(Long id) {
        CanakNumaralar canakNumaralar = dbCanakNumaralarService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Çanak numara bulunamadı: " + id));
        return canakNumaralarMapper.toDto(canakNumaralar);
    }

    /**
     * Get canak numaralar by canak no
     * @param canakNo Canak no
     * @return CanakNumaralarDTO
     */
    public CanakNumaralarDTO findByCanakNo(String canakNo) {
        CanakNumaralar canakNumaralar = dbCanakNumaralarService.findByCanakNo(canakNo)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Çanak numara bulunamadı: " + canakNo));
        return canakNumaralarMapper.toDto(canakNumaralar);
    }

    /**
     * Search canak numaralar by canak no
     * @param canakNo Canak no
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByCanakNoContainingIgnoreCase(String canakNo) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByCanakNoContainingIgnoreCase(canakNo);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get canak numaralar by kurum kod
     * @param kurumKod Kurum kod
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByKurumKod(String kurumKod) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByKurumKod(kurumKod);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get canak numaralar by ekleyen id
     * @param ekleyenId Ekleyen id
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByEkleyenId(Long ekleyenId) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByEkleyenId(ekleyenId);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get canak numaralar by kutu
     * @param kutu Kutu
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByKutu(Long kutu) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByKutu(kutu);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get canak numaralar by ekleme tarih between
     * @param startDate Start date
     * @param endDate End date
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByEklemeTarihBetween(Date startDate, Date endDate) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByEklemeTarihBetween(startDate, endDate);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Search canak numaralar by aciklama
     * @param aciklama Aciklama
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByAciklamaContainingIgnoreCase(String aciklama) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByAciklamaContainingIgnoreCase(aciklama);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get canak numaralar by kurum kod and kutu
     * @param kurumKod Kurum kod
     * @param kutu Kutu
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByKurumKodAndKutu(String kurumKod, Long kutu) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByKurumKodAndKutu(kurumKod, kutu);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Get canak numaralar by canak no starting with
     * @param prefix Prefix
     * @return List of CanakNumaralarDTO
     */
    public List<CanakNumaralarDTO> findByCanakNoStartingWith(String prefix) {
        List<CanakNumaralar> canakNumaralarList = dbCanakNumaralarService.findByCanakNoStartingWith(prefix);
        return canakNumaralarMapper.toDtoList(canakNumaralarList);
    }

    /**
     * Check if canak no exists
     * @param canakNo Canak no
     * @return true if exists, false otherwise
     */
    public boolean existsByCanakNo(String canakNo) {
        return dbCanakNumaralarService.existsByCanakNo(canakNo);
    }

    /**
     * Create new canak numaralar
     * @param canakNumaralarDTO CanakNumaralarDTO
     * @return Created CanakNumaralarDTO
     */
    public CanakNumaralarDTO create(CanakNumaralarDTO canakNumaralarDTO) {
        // Check if canak no already exists
        if (canakNumaralarDTO.getCanakNo() != null && dbCanakNumaralarService.existsByCanakNo(canakNumaralarDTO.getCanakNo())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu çanak numarası zaten mevcut: " + canakNumaralarDTO.getCanakNo());
        }

        // Set ekleme tarih if not provided
        if (canakNumaralarDTO.getEklemeTarih() == null) {
            canakNumaralarDTO.setEklemeTarih(new Date());
        }

        CanakNumaralar canakNumaralar = canakNumaralarMapper.toEntity(canakNumaralarDTO);
        dbCanakNumaralarService.save(canakNumaralar);
        log.info("Çanak numara oluşturuldu: {}", canakNumaralar.getCanakNo());
        return canakNumaralarMapper.toDto(canakNumaralar);
    }

    /**
     * Update existing canak numaralar
     * @param id CanakNumaralar id
     * @param canakNumaralarDTO CanakNumaralarDTO
     * @return Updated CanakNumaralarDTO
     */
    public CanakNumaralarDTO update(Long id, CanakNumaralarDTO canakNumaralarDTO) {
        CanakNumaralar existingCanakNumaralar = dbCanakNumaralarService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Çanak numara bulunamadı: " + id));

        // Check if canak no already exists (if changed)
        if (canakNumaralarDTO.getCanakNo() != null && 
            !canakNumaralarDTO.getCanakNo().equals(existingCanakNumaralar.getCanakNo()) && 
            dbCanakNumaralarService.existsByCanakNo(canakNumaralarDTO.getCanakNo())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu çanak numarası zaten mevcut: " + canakNumaralarDTO.getCanakNo());
        }

        CanakNumaralar updatedCanakNumaralar = canakNumaralarMapper.updateEntityFromDto(existingCanakNumaralar, canakNumaralarDTO);
        dbCanakNumaralarService.update(updatedCanakNumaralar);
        log.info("Çanak numara güncellendi: {}", updatedCanakNumaralar.getCanakNo());
        return canakNumaralarMapper.toDto(updatedCanakNumaralar);
    }

    /**
     * Delete canak numaralar
     * @param id CanakNumaralar id
     */
    public void delete(Long id) {
        CanakNumaralar canakNumaralar = dbCanakNumaralarService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Çanak numara bulunamadı: " + id));
        dbCanakNumaralarService.delete(canakNumaralar);
        log.info("Çanak numara silindi: {}", canakNumaralar.getCanakNo());
    }
}
