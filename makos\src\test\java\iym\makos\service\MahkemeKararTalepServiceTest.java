package iym.makos.service;

import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.makos.mapper.MahkemeKararTalepMapper;
import iym.makos.service.makos.MahkemeKararTalepService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.dto.MahkemeKararTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doNothing;

@ExtendWith(MockitoExtension.class)
class MahkemeKararTalepServiceTest {

    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @Mock
    private MahkemeKararTalepMapper mahkemeKararTalepMapper;

    @InjectMocks
    private MahkemeKararTalepService mahkemeKararTalepService;

    private MahkemeKararTalep mahkemeKararTalep;
    private MahkemeKararTalepDTO mahkemeKararTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();
        mahkemeKararTalep = MahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahKararBasTar(testDate)
                .mahKararBitisTar(testDate)
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeKararNo("2023/123")
                .mahkemeIlIlceKodu("0600")
                .aciklama("Test açıklama")
                .hakimSicilNo("12345")
                .sorusturmaNo("2023/456")
                .gercekMahId(300L)
                .mahkemeKodu("SC01")
                .build();

        mahkemeKararTalepDTO = MahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahKararBasTar(testDate)
                .mahKararBitisTar(testDate)
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeKararNo("2023/123")
                .mahkemeIlIlceKodu("0600")
                .aciklama("Test açıklama")
                .hakimSicilNo("12345")
                .sorusturmaNo("2023/456")
                .gercekMahId(300L)
                .mahkemeKodu("SC01")
                .build();
    }

    @Test
    void findAll_shouldReturnListOfMahkemeKararTalepDTO() {
        // Given
        List<MahkemeKararTalep> mahkemeKararTalepList = Arrays.asList(mahkemeKararTalep);
        List<MahkemeKararTalepDTO> expectedDtoList = Arrays.asList(mahkemeKararTalepDTO);

        when(dbMahkemeKararTalepService.findAll()).thenReturn(mahkemeKararTalepList);
        when(mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList)).thenReturn(expectedDtoList);

        // When
        List<MahkemeKararTalepDTO> result = mahkemeKararTalepService.findAll();

        // Then
        assertThat(result).isEqualTo(expectedDtoList);
        verify(dbMahkemeKararTalepService).findAll();
        verify(mahkemeKararTalepMapper).toDtoList(mahkemeKararTalepList);
    }

    @Test
    void findById_shouldReturnMahkemeKararTalepDTO_whenFound() {
        // Given
        Long id = 1L;
        when(dbMahkemeKararTalepService.findById(id)).thenReturn(Optional.of(mahkemeKararTalep));
        when(mahkemeKararTalepMapper.toDto(mahkemeKararTalep)).thenReturn(mahkemeKararTalepDTO);

        // When
        MahkemeKararTalepDTO result = mahkemeKararTalepService.findById(id);

        // Then
        assertThat(result).isEqualTo(mahkemeKararTalepDTO);
        verify(dbMahkemeKararTalepService).findById(id);
        verify(mahkemeKararTalepMapper).toDto(mahkemeKararTalep);
    }

    @Test
    void findById_shouldThrowResponseStatusException_whenNotFound() {
        // Given
        Long id = 1L;
        when(dbMahkemeKararTalepService.findById(id)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResponseStatusException.class, () -> mahkemeKararTalepService.findById(id));
        verify(dbMahkemeKararTalepService).findById(id);
        verify(mahkemeKararTalepMapper, never()).toDto(any());
    }

    @Test
    void findByEvrakId_shouldReturnListOfMahkemeKararTalepDTO() {
        // Given
        Long evrakId = 100L;
        List<MahkemeKararTalep> mahkemeKararTalepList = Arrays.asList(mahkemeKararTalep);
        List<MahkemeKararTalepDTO> expectedDtoList = Arrays.asList(mahkemeKararTalepDTO);

        when(dbMahkemeKararTalepService.findByEvrakId(evrakId)).thenReturn(mahkemeKararTalepList);
        when(mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList)).thenReturn(expectedDtoList);

        // When
        List<MahkemeKararTalepDTO> result = mahkemeKararTalepService.findByEvrakId(evrakId);

        // Then
        assertThat(result).isEqualTo(expectedDtoList);
        verify(dbMahkemeKararTalepService).findByEvrakId(evrakId);
        verify(mahkemeKararTalepMapper).toDtoList(mahkemeKararTalepList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfMahkemeKararTalepDTO() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        List<MahkemeKararTalep> mahkemeKararTalepList = Arrays.asList(mahkemeKararTalep);
        Page<MahkemeKararTalep> mahkemeKararTalepPage = new PageImpl<>(mahkemeKararTalepList, pageable, 1);
        List<MahkemeKararTalepDTO> dtoList = Arrays.asList(mahkemeKararTalepDTO);

        when(dbMahkemeKararTalepService.findAll(pageable)).thenReturn(mahkemeKararTalepPage);
        when(mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList)).thenReturn(dtoList);

        // When
        Page<MahkemeKararTalepDTO> result = mahkemeKararTalepService.findAll(pageable);

        // Then
        assertThat(result.getContent()).isEqualTo(dtoList);
        assertThat(result.getTotalElements()).isEqualTo(1);
        verify(dbMahkemeKararTalepService).findAll(pageable);
        verify(mahkemeKararTalepMapper).toDtoList(mahkemeKararTalepList);
    }


    @Test
    void update_shouldReturnUpdatedMahkemeKararTalepDTO() {
        // Given
        Long id = 1L;
        MahkemeKararTalep existingEntity = MahkemeKararTalep.builder()
                .id(id)
                .mahkemeKararNo("2023/123")
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeIlIlceKodu("0600")
                .build();

        MahkemeKararTalep updatedEntity = MahkemeKararTalep.builder()
                .id(id)
                .mahkemeKararNo("2023/456")
                .mahkemeAdi("ANKARA 2. SULH CEZA HAKİMLİĞİ")
                .mahkemeIlIlceKodu("0600")
                .build();

        MahkemeKararTalepDTO updateDto = MahkemeKararTalepDTO.builder()
                .id(id)
                .mahkemeKararNo("2023/456")
                .mahkemeAdi("ANKARA 2. SULH CEZA HAKİMLİĞİ")
                .mahkemeIlIlceKodu("0600")
                .build();

        when(dbMahkemeKararTalepService.findById(id)).thenReturn(Optional.of(existingEntity));
        when(mahkemeKararTalepMapper.updateEntityFromDto(existingEntity, updateDto)).thenReturn(updatedEntity);
        doNothing().when(dbMahkemeKararTalepService).update(updatedEntity);
        when(mahkemeKararTalepMapper.toDto(updatedEntity)).thenReturn(updateDto);

        // When
        MahkemeKararTalepDTO result = mahkemeKararTalepService.update(id, updateDto);

        // Then
        assertThat(result).isEqualTo(updateDto);
        verify(dbMahkemeKararTalepService).findById(id);
        verify(mahkemeKararTalepMapper).updateEntityFromDto(existingEntity, updateDto);
        verify(dbMahkemeKararTalepService).update(updatedEntity);
        verify(mahkemeKararTalepMapper).toDto(updatedEntity);
    }

    @Test
    void delete_shouldDeleteMahkemeKararTalep() {
        // Given
        Long id = 1L;
        when(dbMahkemeKararTalepService.findById(id)).thenReturn(Optional.of(mahkemeKararTalep));

        // When
        mahkemeKararTalepService.delete(id);

        // Then
        verify(dbMahkemeKararTalepService).findById(id);
        verify(dbMahkemeKararTalepService).delete(mahkemeKararTalep);
    }

    @Test
    void delete_shouldThrowResponseStatusException_whenNotFound() {
        // Given
        Long id = 1L;
        when(dbMahkemeKararTalepService.findById(id)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResponseStatusException.class, () -> mahkemeKararTalepService.delete(id));
        verify(dbMahkemeKararTalepService).findById(id);
        verify(dbMahkemeKararTalepService, never()).delete(any());
    }
}
