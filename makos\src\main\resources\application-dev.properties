# Development Environment Configuration for Makos

# =============================================================================
# ORACLE DATABASE CONFIGURATION for development
# =============================================================================
spring.datasource.oracle.jdbcUrl=***********************************
spring.datasource.oracle.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=iym
spring.datasource.oracle.password=iym

# Oracle HikariCP Configuration for development
spring.datasource.oracle.hikari.connectionTimeout=20000
spring.datasource.oracle.hikari.maximumPoolSize=10
spring.datasource.oracle.hikari.minimumIdle=5
spring.datasource.oracle.hikari.idleTimeout=300000
spring.datasource.oracle.hikari.maxLifetime=1200000
spring.datasource.oracle.hikari.leakDetectionThreshold=60000
spring.datasource.oracle.hikari.poolName=OracleHikariPool

spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.properties.hibernate.default_schema=iym

# JPA configuration for development
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.hbm2ddl.auto=validate

# Development specific logging
logging.level.root=INFO
logging.level.iym=DEBUG
logging.level.org.hibernate=ERROR

# Security debugging - VERY DETAILED
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web=DEBUG
logging.level.org.springframework.security.authentication=DEBUG
logging.level.org.springframework.security.core=DEBUG
logging.level.org.springframework.web.filter=DEBUG

# Database and JPA debugging
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.transaction=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG

# HTTP request debugging
logging.level.org.springframework.web=DEBUG
logging.level.org.apache.http=DEBUG



# Application specific properties for development
app.init-db=true

# CORS Configuration for development (specific local origins)
cors.allowed.origins=http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000,http://localhost:8080
