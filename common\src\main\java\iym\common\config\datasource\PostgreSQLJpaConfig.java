package iym.common.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * PostgreSQL Database JPA Configuration (Secondary DataSource)
 * Handles PostgreSQL entities and repositories for future use
 * 
 * Activation: Set spring.datasource.postgresql.enabled=true in application.properties
 */
@Configuration
@EnableTransactionManagement
@ConditionalOnProperty(name = "spring.datasource.postgresql.enabled", havingValue = "true")
@ComponentScan({
    "iym.db.jpa.postgresql.service.impl",  // PostgreSQL service implementations
    "iym.common.service.db.postgresql"     // PostgreSQL-specific service interfaces
})
@EnableJpaRepositories(
    basePackages = {
        "iym.db.jpa.postgresql.dao"  // PostgreSQL-specific repositories
    },
    entityManagerFactoryRef = "postgresqlEntityManagerFactory",
    transactionManagerRef = "postgresqlTransactionManager"
)
public class PostgreSQLJpaConfig {

    /**
     * PostgreSQL DataSource Configuration (Secondary)
     * Maps to spring.datasource.postgresql.* properties
     */
    @Bean(name = "postgresqlDataSource")
    @ConfigurationProperties("spring.datasource.postgresql")
    public DataSource postgresqlDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * PostgreSQL EntityManagerFactory Configuration (Secondary)
     * Handles PostgreSQL-specific entities and JPA settings
     */
    @Bean(name = "postgresqlEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean postgresqlEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("postgresqlDataSource") DataSource dataSource) {
        
        Map<String, Object> properties = getPostgreSQLJpaProperties();
        
        return builder
                .dataSource(dataSource)
                .packages(
                    "iym.common.model.entity.postgresql"  // PostgreSQL entities package
                )
                .persistenceUnit("postgresql")
                .properties(properties)
                .build();
    }

    /**
     * PostgreSQL Transaction Manager Configuration (Secondary)
     * Manages transactions for PostgreSQL datasource
     */
    @Bean(name = "postgresqlTransactionManager")
    public PlatformTransactionManager postgresqlTransactionManager(
            @Qualifier("postgresqlEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager(entityManagerFactory);
        transactionManager.setNestedTransactionAllowed(true);
        return transactionManager;
    }

    /**
     * PostgreSQL-specific JPA properties
     * Optimized for PostgreSQL databases
     */
    private Map<String, Object> getPostgreSQLJpaProperties() {
        Map<String, Object> properties = new HashMap<>();
        
        // Hibernate Core Settings
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.default_schema", "public");
        
        // Performance Settings
        properties.put("hibernate.jdbc.batch_size", "25");
        properties.put("hibernate.jdbc.fetch_size", "100");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.batch_versioned_data", "true");
        
        // Connection Settings
        properties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
        properties.put("hibernate.connection.provider_disables_autocommit", "true");
        
        // Logging Settings (can be disabled in production)
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hibernate.use_sql_comments", "false");
        
        // PostgreSQL Specific Settings
        properties.put("hibernate.connection.autocommit", "false");
        
        return properties;
    }
}
