package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.HedeflerAidiyatTalep;
import org.springframework.stereotype.Component;
import iym.makos.dto.HedeflerAidiyatTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for HedeflerAidiyatTalep entity and DTO
 */
@Component
public class HedeflerAidiyatTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity HedeflerAidiyatTalep entity
     * @return HedeflerAidiyatTalepDTO
     */
    public HedeflerAidiyatTalepDTO toDto(HedeflerAidiyatTalep entity) {
        if (entity == null) {
            return null;
        }

        return HedeflerAidiyatTalepDTO.builder()
                .id(entity.getId())
                .hedefTalepId(entity.getHedefTalepId())
                .aidiyatKod(entity.getAidiyatKod())
                .tarih(entity.getTarih())
                .kullaniciId(entity.getKullaniciId())
                .durumu(entity.getDurumu())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto HedeflerAidiyatTalepDTO
     * @return HedeflerAidiyatTalep entity
     */
    public HedeflerAidiyatTalep toEntity(HedeflerAidiyatTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return HedeflerAidiyatTalep.builder()
                .id(dto.getId())
                .hedefTalepId(dto.getHedefTalepId())
                .aidiyatKod(dto.getAidiyatKod())
                .tarih(dto.getTarih())
                .kullaniciId(dto.getKullaniciId())
                .durumu(dto.getDurumu())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity HedeflerAidiyatTalep entity to update
     * @param dto HedeflerAidiyatTalepDTO with new values
     * @return Updated HedeflerAidiyatTalep entity
     */
    public HedeflerAidiyatTalep updateEntityFromDto(HedeflerAidiyatTalep entity, HedeflerAidiyatTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setHedefTalepId(dto.getHedefTalepId());
        entity.setAidiyatKod(dto.getAidiyatKod());
        entity.setTarih(dto.getTarih());
        entity.setKullaniciId(dto.getKullaniciId());
        entity.setDurumu(dto.getDurumu());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of HedeflerAidiyatTalep entities
     * @return List of HedeflerAidiyatTalepDTO
     */
    public List<HedeflerAidiyatTalepDTO> toDtoList(List<HedeflerAidiyatTalep> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of HedeflerAidiyatTalepDTO
     * @return List of HedeflerAidiyatTalep entities
     */
    public List<HedeflerAidiyatTalep> toEntityList(List<HedeflerAidiyatTalepDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
