package iym.makos.service.makos;

import iym.common.model.entity.oracle.iym.HedefTipleri;
import iym.common.service.db.DbHedefTipleriService;
import iym.makos.dto.HedefTipleriDTO;
import iym.makos.mapper.HedefTipleriMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for HedefTipleri operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HedefTipleriService {

    private final DbHedefTipleriService dbHedefTipleriService;
    private final HedefTipleriMapper hedefTipleriMapper;

    /**
     * Get all hedef tipleri records
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findAll() {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findAll();
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get all hedef tipleri records with pagination
     * @param pageable Pagination information
     * @return Page of HedefTipleriDTO
     */
    public Page<HedefTipleriDTO> findAll(Pageable pageable) {
        Page<HedefTipleri> hedefTipleriPage = dbHedefTipleriService.findAll(pageable);
        List<HedefTipleriDTO> dtoList = hedefTipleriMapper.toDtoList(hedefTipleriPage.getContent());
        return new PageImpl<>(dtoList, pageable, hedefTipleriPage.getTotalElements());
    }

    /**
     * Get hedef tipleri by id (hedefKodu)
     * @param hedefKodu HedefTipleri hedefKodu
     * @return HedefTipleriDTO
     */
    public HedefTipleriDTO findByHedefKodu(Long hedefKodu) {
        HedefTipleri hedefTipleri = dbHedefTipleriService.findByHedefKodu(hedefKodu)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedef tipi bulunamadı: " + hedefKodu));
        return hedefTipleriMapper.toDto(hedefTipleri);
    }

    /**
     * Get hedef tipleri by hedef tipi
     * @param hedefTipi Hedef tipi
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findByHedefTipi(String hedefTipi) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findByHedefTipi(hedefTipi);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get hedef tipleri by sonlandirmami
     * @param sonlandirmami Sonlandirmami
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findBySonlandirmami(String sonlandirmami) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findBySonlandirmami(sonlandirmami);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get hedef tipleri by durum
     * @param durum Durum
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findByDurum(String durum) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findByDurum(durum);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get hedef tipleri by hedef tanim
     * @param hedefTanim Hedef tanim
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findByHedefTanim(String hedefTanim) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findByHedefTanim(hedefTanim);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get hedef tipleri by aktifmi
     * @param aktifmi Aktifmi
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findByAktifmi(Long aktifmi) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findByAktifmi(aktifmi);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get hedef tipleri by hitapa gonderilecekmi
     * @param hitapaGonderilecekmi Hitapa gonderilecekmi
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findByHitapaGonderilecekmi(Long hitapaGonderilecekmi) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findByHitapaGonderilecekmi(hitapaGonderilecekmi);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get hedef tipleri by hedef tipi containing
     * @param hedefTipi Hedef tipi
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findByHedefTipiContainingIgnoreCase(String hedefTipi) {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findByHedefTipiContainingIgnoreCase(hedefTipi);
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Get all hedef tipleri ordered by sno
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> findAllByOrderBySnoAsc() {
        List<HedefTipleri> hedefTipleriList = dbHedefTipleriService.findAllByOrderBySnoAsc();
        return hedefTipleriMapper.toDtoList(hedefTipleriList);
    }

    /**
     * Create new hedef tipleri
     * @param hedefTipleriDTO HedefTipleriDTO
     * @return Created HedefTipleriDTO
     */
    public HedefTipleriDTO create(HedefTipleriDTO hedefTipleriDTO) {
        // Check if hedef tipleri already exists
        if (hedefTipleriDTO.getHedefKodu() != null) {
            Optional<HedefTipleri> existingHedefTipleri = dbHedefTipleriService.findByHedefKodu(hedefTipleriDTO.getHedefKodu());
            if (existingHedefTipleri.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu hedef tipi zaten mevcut: " + hedefTipleriDTO.getHedefKodu());
            }
        }

        HedefTipleri hedefTipleri = hedefTipleriMapper.toEntity(hedefTipleriDTO);
        dbHedefTipleriService.save(hedefTipleri);
        log.info("Hedef tipi oluşturuldu: {}", hedefTipleri.getHedefKodu());
        return hedefTipleriMapper.toDto(hedefTipleri);
    }

    /**
     * Update existing hedef tipleri
     * @param hedefKodu HedefTipleri hedefKodu
     * @param hedefTipleriDTO HedefTipleriDTO
     * @return Updated HedefTipleriDTO
     */
    public HedefTipleriDTO update(Long hedefKodu, HedefTipleriDTO hedefTipleriDTO) {
        HedefTipleri existingHedefTipleri = dbHedefTipleriService.findByHedefKodu(hedefKodu)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedef tipi bulunamadı: " + hedefKodu));

        // Ensure the DTO has the correct hedefKodu
        hedefTipleriDTO.setHedefKodu(hedefKodu);

        HedefTipleri updatedHedefTipleri = hedefTipleriMapper.updateEntityFromDto(existingHedefTipleri, hedefTipleriDTO);
        dbHedefTipleriService.update(updatedHedefTipleri);
        log.info("Hedef tipi güncellendi: {}", updatedHedefTipleri.getHedefKodu());
        return hedefTipleriMapper.toDto(updatedHedefTipleri);
    }

    /**
     * Delete hedef tipleri
     * @param hedefKodu HedefTipleri hedefKodu
     */
    public void delete(Long hedefKodu) {
        HedefTipleri hedefTipleri = dbHedefTipleriService.findByHedefKodu(hedefKodu)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedef tipi bulunamadı: " + hedefKodu));
        dbHedefTipleriService.delete(hedefTipleri);
        log.info("Hedef tipi silindi: {}", hedefKodu);
    }
}
