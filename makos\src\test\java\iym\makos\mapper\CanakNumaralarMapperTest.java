package iym.makos.mapper;

import iym.common.model.entity.oracle.iym.CanakNumaralar;
import iym.makos.dto.CanakNumaralarDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class CanakNumaralarMapperTest {

    private CanakNumaralarMapper canakNumaralarMapper;
    private CanakNumaralar canakNumaralar;
    private CanakNumaralarDTO canakNumaralarDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        canakNumaralarMapper = new CanakNumaralarMapper();
        testDate = new Date();

        canakNumaralar = CanakNumaralar.builder()
                .id(1L)
                .canakNo("CANAK001")
                .kurumKod("01")
                .eklemeTarih(testDate)
                .kutu(1L)
                .aciklama("Test açıklama")
                .ekleyenId(100L)
                .build();

        canakNumaralarDTO = CanakNumaralarDTO.builder()
                .id(1L)
                .canakNo("CANAK001")
                .kurumKod("01")
                .eklemeTarih(testDate)
                .kutu(1L)
                .aciklama("Test açıklama")
                .ekleyenId(100L)
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        CanakNumaralarDTO result = canakNumaralarMapper.toDto(canakNumaralar);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(canakNumaralar.getId());
        assertThat(result.getCanakNo()).isEqualTo(canakNumaralar.getCanakNo());
        assertThat(result.getKurumKod()).isEqualTo(canakNumaralar.getKurumKod());
        assertThat(result.getEklemeTarih()).isEqualTo(canakNumaralar.getEklemeTarih());
        assertThat(result.getKutu()).isEqualTo(canakNumaralar.getKutu());
        assertThat(result.getAciklama()).isEqualTo(canakNumaralar.getAciklama());
        assertThat(result.getEkleyenId()).isEqualTo(canakNumaralar.getEkleyenId());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        CanakNumaralarDTO result = canakNumaralarMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        CanakNumaralar result = canakNumaralarMapper.toEntity(canakNumaralarDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(canakNumaralarDTO.getId());
        assertThat(result.getCanakNo()).isEqualTo(canakNumaralarDTO.getCanakNo());
        assertThat(result.getKurumKod()).isEqualTo(canakNumaralarDTO.getKurumKod());
        assertThat(result.getEklemeTarih()).isEqualTo(canakNumaralarDTO.getEklemeTarih());
        assertThat(result.getKutu()).isEqualTo(canakNumaralarDTO.getKutu());
        assertThat(result.getAciklama()).isEqualTo(canakNumaralarDTO.getAciklama());
        assertThat(result.getEkleyenId()).isEqualTo(canakNumaralarDTO.getEkleyenId());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        CanakNumaralar result = canakNumaralarMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        CanakNumaralar existingEntity = CanakNumaralar.builder()
                .id(1L)
                .canakNo("CANAK001")
                .kurumKod("01")
                .eklemeTarih(testDate)
                .kutu(1L)
                .aciklama("Test açıklama")
                .ekleyenId(100L)
                .build();

        CanakNumaralarDTO updatedDto = CanakNumaralarDTO.builder()
                .id(1L)
                .canakNo("CANAK002")
                .kurumKod("02")
                .eklemeTarih(testDate)
                .kutu(2L)
                .aciklama("Updated açıklama")
                .ekleyenId(101L)
                .build();

        // When
        CanakNumaralar result = canakNumaralarMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getCanakNo()).isEqualTo("CANAK002");
        assertThat(result.getKurumKod()).isEqualTo("02");
        assertThat(result.getEklemeTarih()).isEqualTo(testDate);
        assertThat(result.getKutu()).isEqualTo(2L);
        assertThat(result.getAciklama()).isEqualTo("Updated açıklama");
        assertThat(result.getEkleyenId()).isEqualTo(101L);
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        CanakNumaralar existingEntity = CanakNumaralar.builder()
                .id(1L)
                .canakNo("CANAK001")
                .kurumKod("01")
                .eklemeTarih(testDate)
                .kutu(1L)
                .aciklama("Test açıklama")
                .ekleyenId(100L)
                .build();

        // When
        CanakNumaralar result = canakNumaralarMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        CanakNumaralar result = canakNumaralarMapper.updateEntityFromDto(null, canakNumaralarDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<CanakNumaralar> entityList = Arrays.asList(canakNumaralar, canakNumaralar);

        // When
        List<CanakNumaralarDTO> result = canakNumaralarMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(canakNumaralar.getId());
        assertThat(result.get(1).getId()).isEqualTo(canakNumaralar.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<CanakNumaralarDTO> result = canakNumaralarMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<CanakNumaralarDTO> dtoList = Arrays.asList(canakNumaralarDTO, canakNumaralarDTO);

        // When
        List<CanakNumaralar> result = canakNumaralarMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(canakNumaralarDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(canakNumaralarDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<CanakNumaralar> result = canakNumaralarMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
