package iym.db.jpa.oracle.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.oracle.iym.EvrakGelenKurumlar;
import iym.common.service.db.DbEvrakGelenKurumlarService;
import iym.db.jpa.oracle.dao.EvrakGelenKurumlarRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for EvrakGelenKurumlar entity
 */
@Service
public class DbEvrakGelenKurumlarServiceImpl extends GenericDbServiceImpl<EvrakGelenKurumlar, Long> implements DbEvrakGelenKurumlarService {

    private final EvrakGelenKurumlarRepo evrakGelenKurumlarRepo;

    @Autowired
    public DbEvrakGelenKurumlarServiceImpl(EvrakGelenKurumlarRepo repository) {
        super(repository);
        this.evrakGelenKurumlarRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EvrakGelenKurumlar> findByKurumKod(String kurumKod) {
        return evrakGelenKurumlarRepo.findByKurumKod(kurumKod);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findByKurumAdi(String kurumAdi) {
        return evrakGelenKurumlarRepo.findByKurumAdi(kurumAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findByKurum(String kurum) {
        return evrakGelenKurumlarRepo.findByKurum(kurum);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findByIdx(Long idx) {
        return evrakGelenKurumlarRepo.findByIdx(idx);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findByKurumAdiContainingIgnoreCase(String kurumAdi) {
        return evrakGelenKurumlarRepo.findByKurumAdiContainingIgnoreCase(kurumAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findByKurumContainingIgnoreCase(String kurum) {
        return evrakGelenKurumlarRepo.findByKurumContainingIgnoreCase(kurum);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findByKurumKodContaining(String kurumKod) {
        return evrakGelenKurumlarRepo.findByKurumKodContaining(kurumKod);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakGelenKurumlar> findAllByOrderByIdxAsc() {
        return evrakGelenKurumlarRepo.findAllByOrderByIdxAsc();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByKurumKod(String kurumKod) {
        return evrakGelenKurumlarRepo.existsByKurumKod(kurumKod);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByKurumAdi(String kurumAdi) {
        return evrakGelenKurumlarRepo.existsByKurumAdi(kurumAdi);
    }
}
