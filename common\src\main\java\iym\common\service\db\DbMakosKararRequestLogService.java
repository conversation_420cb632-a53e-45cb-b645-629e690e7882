package iym.common.service.db;

import iym.common.model.entity.oracle.makos.log.MakosKararRequestLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Service interface for MakosKararRequestLog entity
 * Provides business logic methods for MAKOS court decision request logging
 */
public interface DbMakosKararRequestLogService extends GenericDbService<MakosKararRequestLog, UUID> {

    /**
     * Find request logs by username
     * @param username the username to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByUsername(String username);

    /**
     * Find request logs by acting username
     * @param actingUsername the acting username to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByActingUsername(String actingUsername);

    /**
     * Find request logs by user IP
     * @param userIp the IP address to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByUserIp(String userIp);

    /**
     * Find request logs by mahkeme karar number
     * @param mahkemeKararNo the court decision number to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByMahkemeKararNo(String mahkemeKararNo);

    /**
     * Find request logs by sorusturma number
     * @param sorusturmaNo the investigation number to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findBySorusturmaNo(String sorusturmaNo);

    /**
     * Find request logs by target ID
     * @param targetId the target ID to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByTargetId(String targetId);

    /**
     * Find request logs within a time range
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find request logs by username and time range
     * @param username the username to search for
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByUsernameAndRequestTimeBetween(String username, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find request logs by mahkeme karar number and time range
     * @param mahkemeKararNo the court decision number to search for
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByMahkemeKararNoAndRequestTimeBetween(String mahkemeKararNo, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find request logs by response code
     * @param responseCode the HTTP response code to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByResponseCode(Integer responseCode);

    /**
     * Find all request logs ordered by request time descending
     * @return list of request logs ordered by request time desc
     */
    List<MakosKararRequestLog> findAllByOrderByRequestTimeDesc();

    /**
     * Find request logs by username ordered by request time descending
     * @param username the username to search for
     * @return list of request logs ordered by request time desc
     */
    List<MakosKararRequestLog> findByUsernameOrderByRequestTimeDesc(String username);

    /**
     * Find request logs by mahkeme karar number ordered by request time descending
     * @param mahkemeKararNo the court decision number to search for
     * @return list of request logs ordered by request time desc
     */
    List<MakosKararRequestLog> findByMahkemeKararNoOrderByRequestTimeDesc(String mahkemeKararNo);

    /**
     * Find request logs by request URL containing pattern
     * @param urlPattern the URL pattern to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByRequestURLContaining(String urlPattern);

    /**
     * Find request logs by request ID
     * @param requestId the request ID to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByRequestId(UUID requestId);

    /**
     * Find all request logs with pagination and specification
     * @param spec the specification for filtering
     * @param pageable the pagination information
     * @return page of request logs
     */
    Page<MakosKararRequestLog> findAll(Specification<MakosKararRequestLog> spec, Pageable pageable);

    /**
     * Find request logs by evrak no
     * @param evrakNo the evrak no to search for
     * @return list of request logs
     */
    List<MakosKararRequestLog> findByEvrakNo(String evrakNo);

    /**
     * Find request logs by evrak no ordered by request time descending
     * @param evrakNo the evrak no to search for
     * @return list of request logs ordered by request time desc
     */
    List<MakosKararRequestLog> findByEvrakNoOrderByRequestTimeDesc(String evrakNo);
}
