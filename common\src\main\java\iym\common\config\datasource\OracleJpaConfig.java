package iym.common.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.persistenceunit.PersistenceUnitManager;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * Oracle Database JPA Configuration (Primary DataSource)
 * Handles all existing Oracle entities and repositories
 * <p>
 * This replaces the single datasource configuration in DbLoader
 */
@Configuration
@EnableTransactionManagement
@ComponentScan({
        "iym.db.jpa.oracle.service.impl",  // Oracle service implementations
        "iym.common.service.db.oracle"     // Oracle-specific service interfaces
})
@EnableJpaRepositories(
        basePackages = {
                "iym.db.jpa.oracle.dao"     // Oracle-specific repositories
        },
        entityManagerFactoryRef = "oracleEntityManagerFactory",
        transactionManagerRef = "oracleTransactionManager"
)
public class OracleJpaConfig {

    /**
     * Oracle DataSource Configuration (Primary)
     * Maps to spring.datasource.oracle.* properties
     */
    @Primary
    @Bean(name = "oracleDataSource")
    @ConfigurationProperties("spring.datasource.oracle")
    public DataSource oracleDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * Oracle EntityManagerFactory Configuration (Primary)
     * Handles Oracle-specific entities and JPA settings
     */
    @Primary
    @Bean(name = "oracleEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean oracleEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("oracleDataSource") DataSource dataSource) {

        Map<String, Object> properties = getOracleJpaProperties();

        return builder
                .dataSource(dataSource)
                .packages(
                        "iym.common.model.entity"    // Current entity package (will be moved to oracle later)
                )
                .persistenceUnit("oracle")
                .properties(properties)
                .build();
    }

    @Bean
    public EntityManagerFactoryBuilder entityManagerFactoryBuilder(
            ObjectProvider<PersistenceUnitManager> persistenceUnitManager) {

        return new EntityManagerFactoryBuilder(
                new HibernateJpaVendorAdapter(),
                new HashMap<>(),
                persistenceUnitManager.getIfAvailable()
        );
    }
    /**
     * Oracle Transaction Manager Configuration (Primary)
     * Manages transactions for Oracle datasource
     */
    @Primary
    @Bean(name = "oracleTransactionManager")
    public PlatformTransactionManager oracleTransactionManager(
            @Qualifier("oracleEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager(entityManagerFactory);
        transactionManager.setNestedTransactionAllowed(true);
        return transactionManager;
    }

    /**
     * Oracle-specific JPA properties
     * Optimized for Oracle 12c+ databases
     */
    private Map<String, Object> getOracleJpaProperties() {
        Map<String, Object> properties = new HashMap<>();

        // Hibernate Core Settings
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.dialect", "org.hibernate.dialect.Oracle12cDialect");
        properties.put("hibernate.default_schema", "IYM");

        // Performance Settings
        properties.put("hibernate.jdbc.batch_size", "25");
        properties.put("hibernate.jdbc.fetch_size", "100");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.batch_versioned_data", "true");

        // Connection Settings
        properties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
        properties.put("hibernate.connection.provider_disables_autocommit", "true");

        // Logging Settings (can be disabled in production)
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hibernate.use_sql_comments", "false");

        // Oracle Specific Settings
        properties.put("hibernate.connection.includeSynonyms", "true");
        properties.put("hibernate.connection.autocommit", "false");

        return properties;
    }
}
