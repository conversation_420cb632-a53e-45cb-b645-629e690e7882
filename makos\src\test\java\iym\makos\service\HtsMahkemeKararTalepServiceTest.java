package iym.makos.service;

import iym.common.model.entity.oracle.iym.EvrakKayit;
import iym.common.model.entity.oracle.iym.HtsMahkemeKararTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbHtsMahkemeKararTalepService;
import iym.makos.mapper.HtsMahkemeKararTalepMapper;
import iym.makos.service.makos.HtsMahkemeKararTalepService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.dto.HtsMahkemeKararTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HtsMahkemeKararTalepServiceTest {

    @Mock
    private DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @Mock
    private DbEvrakKayitService dbEvrakKayitService;

    @Mock
    private HtsMahkemeKararTalepMapper htsMahkemeKararTalepMapper;

    @InjectMocks
    private HtsMahkemeKararTalepService htsMahkemeKararTalepService;

    private HtsMahkemeKararTalep htsMahkemeKararTalep;
    private HtsMahkemeKararTalepDTO htsMahkemeKararTalepDTO;
    private EvrakKayit evrakKayit;
    private List<HtsMahkemeKararTalep> htsMahkemeKararTalepList;
    private List<HtsMahkemeKararTalepDTO> htsMahkemeKararTalepDTOList;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();

        evrakKayit = EvrakKayit.builder()
                .id(100L)
                .build();

        htsMahkemeKararTalep = HtsMahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("ILETISIM_TESPITI")
                .hukukBirim("AĞIR CEZA")
                .mahkemeIli("0600")
                .mahkemeKodu("ACM01")
                .mahkemeAdi("ANKARA 1. AĞIR CEZA MAHKEMESİ")
                .aciklama("Test açıklama")
                .mahkemeKararNo("HTS-2023-001")
                .sorusturmaNo("2023/125")
                .build();

        HtsMahkemeKararTalep htsMahkemeKararTalep2 = HtsMahkemeKararTalep.builder()
                .id(2L)
                .evrakId(101L)
                .kullaniciId(201L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("SINYAL_BILGILERI")
                .hukukBirim("SULH CEZA")
                .mahkemeIli("0601")
                .mahkemeKodu("SCM01")
                .mahkemeAdi("ANKARA 1. SULH CEZA MAHKEMESİ")
                .aciklama("Test açıklama 2")
                .mahkemeKararNo("HTS-2023-002")
                .sorusturmaNo("2023/126")
                .build();

        htsMahkemeKararTalepDTO = HtsMahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("ILETISIM_TESPITI")
                .hukukBirim("AĞIR CEZA")
                .mahkemeIli("0600")
                .mahkemeKodu("ACM01")
                .mahkemeAdi("ANKARA 1. AĞIR CEZA MAHKEMESİ")
                .aciklama("Test açıklama")
                .mahkemeKararNo("HTS-2023-001")
                .sorusturmaNo("2023/125")
                .build();

        HtsMahkemeKararTalepDTO htsMahkemeKararTalepDTO2 = HtsMahkemeKararTalepDTO.builder()
                .id(2L)
                .evrakId(101L)
                .kullaniciId(201L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("SINYAL_BILGILERI")
                .hukukBirim("SULH CEZA")
                .mahkemeIli("0601")
                .mahkemeKodu("SCM01")
                .mahkemeAdi("ANKARA 1. SULH CEZA MAHKEMESİ")
                .aciklama("Test açıklama 2")
                .mahkemeKararNo("HTS-2023-002")
                .sorusturmaNo("2023/126")
                .build();

        htsMahkemeKararTalepList = Arrays.asList(htsMahkemeKararTalep, htsMahkemeKararTalep2);
        htsMahkemeKararTalepDTOList = Arrays.asList(htsMahkemeKararTalepDTO, htsMahkemeKararTalepDTO2);
    }

    @Test
    void findAll_shouldReturnAllHtsMahkemeKararTalep() {
        // Given
        when(dbHtsMahkemeKararTalepService.findAll()).thenReturn(htsMahkemeKararTalepList);
        when(htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList)).thenReturn(htsMahkemeKararTalepDTOList);

        // When
        List<HtsMahkemeKararTalepDTO> result = htsMahkemeKararTalepService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(htsMahkemeKararTalepDTOList);
        verify(dbHtsMahkemeKararTalepService).findAll();
        verify(htsMahkemeKararTalepMapper).toDtoList(htsMahkemeKararTalepList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfHtsMahkemeKararTalep() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<HtsMahkemeKararTalep> htsMahkemeKararTalepPage = new PageImpl<>(htsMahkemeKararTalepList, pageable, htsMahkemeKararTalepList.size());

        when(dbHtsMahkemeKararTalepService.findAll(pageable)).thenReturn(htsMahkemeKararTalepPage);
        when(htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList)).thenReturn(htsMahkemeKararTalepDTOList);

        // When
        Page<HtsMahkemeKararTalepDTO> result = htsMahkemeKararTalepService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(htsMahkemeKararTalepDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbHtsMahkemeKararTalepService).findAll(pageable);
        verify(htsMahkemeKararTalepMapper).toDtoList(htsMahkemeKararTalepList);
    }

    @Test
    void findById_shouldReturnHtsMahkemeKararTalep_whenExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(1L)).thenReturn(Optional.of(htsMahkemeKararTalep));
        when(htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep)).thenReturn(htsMahkemeKararTalepDTO);

        // When
        HtsMahkemeKararTalepDTO result = htsMahkemeKararTalepService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(htsMahkemeKararTalepDTO);
        verify(dbHtsMahkemeKararTalepService).findById(1L);
        verify(htsMahkemeKararTalepMapper).toDto(htsMahkemeKararTalep);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsMahkemeKararTalepService.findById(1L));
        verify(dbHtsMahkemeKararTalepService).findById(1L);
        verify(htsMahkemeKararTalepMapper, never()).toDto(any());
    }

    @Test
    void findByMahkemeKararNo_shouldReturnHtsMahkemeKararTalep_whenExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findByMahkemeKararNo("HTS-2023-001")).thenReturn(Optional.of(htsMahkemeKararTalep));
        when(htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep)).thenReturn(htsMahkemeKararTalepDTO);

        // When
        HtsMahkemeKararTalepDTO result = htsMahkemeKararTalepService.findByMahkemeKararNo("HTS-2023-001");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(htsMahkemeKararTalepDTO);
        verify(dbHtsMahkemeKararTalepService).findByMahkemeKararNo("HTS-2023-001");
        verify(htsMahkemeKararTalepMapper).toDto(htsMahkemeKararTalep);
    }

    @Test
    void findByMahkemeKararNo_shouldThrowException_whenNotExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findByMahkemeKararNo("HTS-2023-001")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsMahkemeKararTalepService.findByMahkemeKararNo("HTS-2023-001"));
        verify(dbHtsMahkemeKararTalepService).findByMahkemeKararNo("HTS-2023-001");
        verify(htsMahkemeKararTalepMapper, never()).toDto(any());
    }

    @Test
    void create_shouldCreateHtsMahkemeKararTalep() {
        // Given
        when(dbEvrakKayitService.findById(100L)).thenReturn(Optional.of(evrakKayit));
        when(dbHtsMahkemeKararTalepService.existsByMahkemeKararNo("HTS-2023-001")).thenReturn(false);
        when(htsMahkemeKararTalepMapper.toEntity(htsMahkemeKararTalepDTO)).thenReturn(htsMahkemeKararTalep);
        when(htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep)).thenReturn(htsMahkemeKararTalepDTO);

        // When
        HtsMahkemeKararTalepDTO result = htsMahkemeKararTalepService.create(htsMahkemeKararTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(htsMahkemeKararTalepDTO);
        verify(dbEvrakKayitService).findById(100L);
        verify(dbHtsMahkemeKararTalepService).existsByMahkemeKararNo("HTS-2023-001");
        verify(htsMahkemeKararTalepMapper).toEntity(htsMahkemeKararTalepDTO);
        verify(dbHtsMahkemeKararTalepService).save(htsMahkemeKararTalep);
        verify(htsMahkemeKararTalepMapper).toDto(htsMahkemeKararTalep);
    }

    @Test
    void create_shouldThrowException_whenEvrakNotExists() {
        // Given
        when(dbEvrakKayitService.findById(100L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsMahkemeKararTalepService.create(htsMahkemeKararTalepDTO));
        verify(dbEvrakKayitService).findById(100L);
        verify(dbHtsMahkemeKararTalepService, never()).existsByMahkemeKararNo(anyString());
        verify(htsMahkemeKararTalepMapper, never()).toEntity(any());
        verify(dbHtsMahkemeKararTalepService, never()).save(any());
        verify(htsMahkemeKararTalepMapper, never()).toDto(any());
    }

    @Test
    void create_shouldThrowException_whenMahkemeKararNoAlreadyExists() {
        // Given
        when(dbEvrakKayitService.findById(100L)).thenReturn(Optional.of(evrakKayit));
        when(dbHtsMahkemeKararTalepService.existsByMahkemeKararNo("HTS-2023-001")).thenReturn(true);

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsMahkemeKararTalepService.create(htsMahkemeKararTalepDTO));
        verify(dbEvrakKayitService).findById(100L);
        verify(dbHtsMahkemeKararTalepService).existsByMahkemeKararNo("HTS-2023-001");
        verify(htsMahkemeKararTalepMapper, never()).toEntity(any());
        verify(dbHtsMahkemeKararTalepService, never()).save(any());
        verify(htsMahkemeKararTalepMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateHtsMahkemeKararTalep() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(1L)).thenReturn(Optional.of(htsMahkemeKararTalep));
        when(htsMahkemeKararTalepMapper.updateEntityFromDto(htsMahkemeKararTalep, htsMahkemeKararTalepDTO)).thenReturn(htsMahkemeKararTalep);
        when(htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep)).thenReturn(htsMahkemeKararTalepDTO);

        // When
        HtsMahkemeKararTalepDTO result = htsMahkemeKararTalepService.update(1L, htsMahkemeKararTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(htsMahkemeKararTalepDTO);
        verify(dbHtsMahkemeKararTalepService).findById(1L);
        verify(htsMahkemeKararTalepMapper).updateEntityFromDto(htsMahkemeKararTalep, htsMahkemeKararTalepDTO);
        verify(dbHtsMahkemeKararTalepService).update(htsMahkemeKararTalep);
        verify(htsMahkemeKararTalepMapper).toDto(htsMahkemeKararTalep);
    }

    @Test
    void update_shouldThrowException_whenHtsMahkemeKararTalepNotExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsMahkemeKararTalepService.update(1L, htsMahkemeKararTalepDTO));
        verify(dbHtsMahkemeKararTalepService).findById(1L);
        verify(htsMahkemeKararTalepMapper, never()).updateEntityFromDto(any(), any());
        verify(dbHtsMahkemeKararTalepService, never()).update(any());
        verify(htsMahkemeKararTalepMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteHtsMahkemeKararTalep() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(1L)).thenReturn(Optional.of(htsMahkemeKararTalep));

        // When
        htsMahkemeKararTalepService.delete(1L);

        // Then
        verify(dbHtsMahkemeKararTalepService).findById(1L);
        verify(dbHtsMahkemeKararTalepService).delete(htsMahkemeKararTalep);
    }

    @Test
    void delete_shouldThrowException_whenHtsMahkemeKararTalepNotExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsMahkemeKararTalepService.delete(1L));
        verify(dbHtsMahkemeKararTalepService).findById(1L);
        verify(dbHtsMahkemeKararTalepService, never()).delete(any());
    }
}
