package iym.db.jpa.oracle.dao;

import iym.common.model.entity.oracle.iym.Iller;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Iller entity
 */
@Repository
public interface IllerRepo extends JpaRepository<Iller, String> {

    Optional<Iller> findByIlKod(String ilKodu);

    List<Iller> findByIlAdi(String ilAdi);
    
    List<Iller> findByIlceAdi(String ilceAdi);
    
    List<Iller> findByIlAdiAndIlceAdi(String ilAdi, String ilceAdi);
    
    List<Iller> findByIlKodStartingWith(String ilKodPrefix);
    
    List<Iller> findByIlAdiContainingIgnoreCase(String ilAdi);
    
    List<Iller> findByIlceAdiContainingIgnoreCase(String ilceAdi);
    
    List<Iller> findAllByOrderByIlAdiAsc();
    
    List<Iller> findAllByOrderByIlKodAsc();
    
    List<Iller> findByIlAdiOrderByIlceAdiAsc(String ilAdi);
    
    boolean existsByIlAdiAndIlceAdi(String ilAdi, String ilceAdi);
}
